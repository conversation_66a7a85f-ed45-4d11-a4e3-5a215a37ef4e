=== TERMINAL OUTPUT LOG ===
Script: 02_download_and_organize
Started: 2025-09-18 09:13:10
Log File: C:\Users\<USER>\Videos\PlexAutomator\logs\02_download_and_organize_2025-09-18_09-13-10-AM.txt
==================================================

[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 📝 Terminal logging started for 02_download_and_organize
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 📄 Log file: C:\Users\<USER>\Videos\PlexAutomator\logs\02_download_and_organize_2025-09-18_09-13-10-AM.txt
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 🕐 Started at: 2025-09-18 09:13:10
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] ------------------------------------------------------------
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] *** UNIFIED Stage 02: Download and Organize ***
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] ==================================================
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] + Consolidated from multiple O2 scripts into one unified implementation
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] >> Modern Radarr API integration
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] -- Simplified workflow: Radarr -> SABnzbd -> Plex
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] >> Clean, maintainable codebase
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]    Default: Interactive mode (use --movies-only, --tv-only, or --all for command-line mode)
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,745 - pipeline_02 - INFO - ===== Starting Pipeline 02 Execution =====
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,747 - pipeline_02 - INFO - Settings loaded successfully
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,747 - pipeline_02 - INFO - Command-line mode: Processing both
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,747 - pipeline_02 - INFO - 🎬 Starting Radarr (Movies) monitoring...
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,747 - pipeline_02 - INFO - ===== Starting Modern Radarr Download Monitoring with SQLite =====
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,747 - pipeline_02 - INFO -      ENHANCED: Dual-detection system (Filesystem + Radarr API) + SQLite state
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,747 - pipeline_02 - INFO - 🔄 Checking for season progression opportunities...
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,747 - pipeline_02 - INFO -      Sequential progression enabled for 0 series
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,747 - pipeline_02 - INFO -      No series opted-in for sequential progression
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,749 - _internal.utils.filesystem_first_state_manager - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,749 - pipeline_02 - INFO - 🔄 Initializing real-time telemetry for download monitoring...
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,750 - pipeline_02 - INFO - No valid active jobs found in state file
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,750 - pipeline_02 - INFO - No movie candidates found in state file
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,751 - pipeline_02 - INFO - 🔄 Real-time telemetry system initialized
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,751 - pipeline_02 - INFO - ✅ Real-time telemetry system initialized for download monitoring
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,751 - pipeline_02 - INFO -    🛡️ Intelligent fallback protection: ENABLED
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,751 - pipeline_02 - INFO - Discovering movies by scanning filesystem...
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,754 - pipeline_02 - INFO - Found 0 movies across 14 stages
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,754 - pipeline_02 - INFO -      SABnzbd complete directory: workspace\1_downloading\complete_raw
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,754 - pipeline_02 - INFO -      Radarr API endpoint: http://localhost:7878
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,754 - pipeline_02 - INFO -      SMART STATE VALIDATION: Checking for inconsistent states...
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,765 - pipeline_02 - INFO - Retrieved 0 movies from Radarr
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,766 - pipeline_02 - INFO - No active downloads in Radarr queue
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,766 - pipeline_02 - INFO - Found 0 movies in download states to monitor
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,767 - pipeline_02 - INFO -      SMART STATE VALIDATION: Checking for inconsistent states...
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,767 - pipeline_02 - INFO - Found 0 movies in download states to monitor
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,767 - pipeline_02 - INFO - No movies currently in download states
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,767 - pipeline_02 - INFO -      ENHANCED: Checking both Radarr API and filesystem for completed downloads
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,767 - pipeline_02 - INFO -      Will scan SABnzbd directory: workspace\1_downloading\complete_raw
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,767 - pipeline_02 - INFO - 🛡️  LONG PATH PRE-PROCESSING: Checking for Windows path length issues...
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,767 - pipeline_02 - INFO -      Found 11 items to check for long paths
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,767 - pipeline_02 - INFO -      Checking folder: Steven.Universe.S01E46.720p.BluRay.x264-TAXES (82 chars)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,768 - pipeline_02 - INFO -           MKV file: Steven.Universe.S01E46.720p.BluRay.x264-TAXES.mkv (132 chars)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,768 - pipeline_02 - INFO -           Full MKV path: workspace\1_downloading\complete_raw\Steven.Universe.S01E46.720p.BluRay.x264-TAXES\Steven.Universe.S01E46.720p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,768 - pipeline_02 - INFO -      Checking folder: Steven.Universe.S02E01.1080p.BluRay.x264-TAXES (83 chars)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,768 - pipeline_02 - INFO -           MKV file: Steven.Universe.S02E01.1080p.BluRay.x264-TAXES.mkv (134 chars)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,768 - pipeline_02 - INFO -           Full MKV path: workspace\1_downloading\complete_raw\Steven.Universe.S02E01.1080p.BluRay.x264-TAXES\Steven.Universe.S02E01.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,768 - pipeline_02 - INFO -      Checking folder: Steven.Universe.S02E02.1080p.BluRay.x264-TAXES (83 chars)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,768 - pipeline_02 - INFO -           MKV file: Steven.Universe.S02E02.1080p.BluRay.x264-TAXES.mkv (134 chars)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,768 - pipeline_02 - INFO -           Full MKV path: workspace\1_downloading\complete_raw\Steven.Universe.S02E02.1080p.BluRay.x264-TAXES\Steven.Universe.S02E02.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,768 - pipeline_02 - INFO -      Checking folder: Steven.Universe.S02E03.1080p.BluRay.x264-TAXES (83 chars)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,768 - pipeline_02 - INFO -           MKV file: Steven.Universe.S02E03.1080p.BluRay.x264-TAXES.mkv (134 chars)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,768 - pipeline_02 - INFO -           Full MKV path: workspace\1_downloading\complete_raw\Steven.Universe.S02E03.1080p.BluRay.x264-TAXES\Steven.Universe.S02E03.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,768 - pipeline_02 - INFO -      Checking folder: Steven.Universe.S02E04.1080p.BluRay.x264-TAXES (83 chars)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,768 - pipeline_02 - INFO -           MKV file: Steven.Universe.S02E04.1080p.BluRay.x264-TAXES.mkv (134 chars)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,768 - pipeline_02 - INFO -           Full MKV path: workspace\1_downloading\complete_raw\Steven.Universe.S02E04.1080p.BluRay.x264-TAXES\Steven.Universe.S02E04.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,768 - pipeline_02 - INFO -      Checking folder: Steven.Universe.S02E05.1080p.BluRay.x264-TAXES (83 chars)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,768 - pipeline_02 - INFO -           MKV file: Steven.Universe.S02E05.1080p.BluRay.x264-TAXES.mkv (134 chars)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,768 - pipeline_02 - INFO -           Full MKV path: workspace\1_downloading\complete_raw\Steven.Universe.S02E05.1080p.BluRay.x264-TAXES\Steven.Universe.S02E05.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,768 - pipeline_02 - INFO -      Checking folder: Steven.Universe.S02E06.1080p.BluRay.x264-TAXES (83 chars)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,768 - pipeline_02 - INFO -           MKV file: Steven.Universe.S02E06.1080p.BluRay.x264-TAXES.mkv (134 chars)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,768 - pipeline_02 - INFO -           Full MKV path: workspace\1_downloading\complete_raw\Steven.Universe.S02E06.1080p.BluRay.x264-TAXES\Steven.Universe.S02E06.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,768 - pipeline_02 - INFO -      Checking folder: Steven.Universe.S02E07.1080p.BluRay.x264-TAXES (83 chars)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,768 - pipeline_02 - INFO -           MKV file: Steven.Universe.S02E07.1080p.BluRay.x264-TAXES.mkv (134 chars)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,768 - pipeline_02 - INFO -           Full MKV path: workspace\1_downloading\complete_raw\Steven.Universe.S02E07.1080p.BluRay.x264-TAXES\Steven.Universe.S02E07.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,768 - pipeline_02 - INFO -      Checking folder: Steven.Universe.S02E08.1080p.BluRay.x264-TAXES (83 chars)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,768 - pipeline_02 - INFO -           MKV file: Steven.Universe.S02E08.1080p.BluRay.x264-TAXES.mkv (134 chars)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,769 - pipeline_02 - INFO -           Full MKV path: workspace\1_downloading\complete_raw\Steven.Universe.S02E08.1080p.BluRay.x264-TAXES\Steven.Universe.S02E08.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,769 - pipeline_02 - INFO -      Checking folder: Steven.Universe.S02E09.1080p.BluRay.x264-TAXES (83 chars)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,769 - pipeline_02 - INFO -           MKV file: Steven.Universe.S02E09.1080p.BluRay.x264-TAXES.mkv (134 chars)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,769 - pipeline_02 - INFO -           Full MKV path: workspace\1_downloading\complete_raw\Steven.Universe.S02E09.1080p.BluRay.x264-TAXES\Steven.Universe.S02E09.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,769 - pipeline_02 - INFO -      Checking folder: Steven.Universe.S02E10.1080p.BluRay.x264-TAXES (83 chars)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,769 - pipeline_02 - INFO -           MKV file: Steven.Universe.S02E10.1080p.BluRay.x264-TAXES.mkv (134 chars)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,769 - pipeline_02 - INFO -           Full MKV path: workspace\1_downloading\complete_raw\Steven.Universe.S02E10.1080p.BluRay.x264-TAXES\Steven.Universe.S02E10.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]   DEBUG: Checking folder: Steven.Universe.S01E46.720p.BluRay.x264-TAXES
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]   DEBUG: Absolute path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S01E46.720p.BluRay.x264-TAXES (118 chars)
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]     DEBUG: MKV file: Steven.Universe.S01E46.720p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]     DEBUG: Absolute MKV path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S01E46.720p.BluRay.x264-TAXES\Steven.Universe.S01E46.720p.BluRay.x264-TAXES.mkv (168 chars)
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]   DEBUG: Checking folder: Steven.Universe.S02E01.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]   DEBUG: Absolute path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E01.1080p.BluRay.x264-TAXES (119 chars)
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]     DEBUG: MKV file: Steven.Universe.S02E01.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]     DEBUG: Absolute MKV path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E01.1080p.BluRay.x264-TAXES\Steven.Universe.S02E01.1080p.BluRay.x264-TAXES.mkv (170 chars)
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]   DEBUG: Checking folder: Steven.Universe.S02E02.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]   DEBUG: Absolute path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E02.1080p.BluRay.x264-TAXES (119 chars)
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]     DEBUG: MKV file: Steven.Universe.S02E02.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]     DEBUG: Absolute MKV path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E02.1080p.BluRay.x264-TAXES\Steven.Universe.S02E02.1080p.BluRay.x264-TAXES.mkv (170 chars)
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]   DEBUG: Checking folder: Steven.Universe.S02E03.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]   DEBUG: Absolute path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E03.1080p.BluRay.x264-TAXES (119 chars)
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]     DEBUG: MKV file: Steven.Universe.S02E03.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]     DEBUG: Absolute MKV path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E03.1080p.BluRay.x264-TAXES\Steven.Universe.S02E03.1080p.BluRay.x264-TAXES.mkv (170 chars)
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]   DEBUG: Checking folder: Steven.Universe.S02E04.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]   DEBUG: Absolute path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E04.1080p.BluRay.x264-TAXES (119 chars)
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]     DEBUG: MKV file: Steven.Universe.S02E04.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]     DEBUG: Absolute MKV path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E04.1080p.BluRay.x264-TAXES\Steven.Universe.S02E04.1080p.BluRay.x264-TAXES.mkv (170 chars)
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]   DEBUG: Checking folder: Steven.Universe.S02E05.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]   DEBUG: Absolute path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E05.1080p.BluRay.x264-TAXES (119 chars)
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]     DEBUG: MKV file: Steven.Universe.S02E05.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]     DEBUG: Absolute MKV path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E05.1080p.BluRay.x264-TAXES\Steven.Universe.S02E05.1080p.BluRay.x264-TAXES.mkv (170 chars)
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]   DEBUG: Checking folder: Steven.Universe.S02E06.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]   DEBUG: Absolute path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E06.1080p.BluRay.x264-TAXES (119 chars)
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]     DEBUG: MKV file: Steven.Universe.S02E06.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]     DEBUG: Absolute MKV path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E06.1080p.BluRay.x264-TAXES\Steven.Universe.S02E06.1080p.BluRay.x264-TAXES.mkv (170 chars)
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]   DEBUG: Checking folder: Steven.Universe.S02E07.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]   DEBUG: Absolute path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E07.1080p.BluRay.x264-TAXES (119 chars)
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]     DEBUG: MKV file: Steven.Universe.S02E07.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]     DEBUG: Absolute MKV path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E07.1080p.BluRay.x264-TAXES\Steven.Universe.S02E07.1080p.BluRay.x264-TAXES.mkv (170 chars)
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]   DEBUG: Checking folder: Steven.Universe.S02E08.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]   DEBUG: Absolute path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E08.1080p.BluRay.x264-TAXES (119 chars)
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]     DEBUG: MKV file: Steven.Universe.S02E08.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]     DEBUG: Absolute MKV path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E08.1080p.BluRay.x264-TAXES\Steven.Universe.S02E08.1080p.BluRay.x264-TAXES.mkv (170 chars)
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]   DEBUG: Checking folder: Steven.Universe.S02E09.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]   DEBUG: Absolute path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E09.1080p.BluRay.x264-TAXES (119 chars)
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]     DEBUG: MKV file: Steven.Universe.S02E09.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]     DEBUG: Absolute MKV path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E09.1080p.BluRay.x264-TAXES\Steven.Universe.S02E09.1080p.BluRay.x264-TAXES.mkv (170 chars)
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]   DEBUG: Checking folder: Steven.Universe.S02E10.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]   DEBUG: Absolute path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E10.1080p.BluRay.x264-TAXES (119 chars)
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]     DEBUG: MKV file: Steven.Universe.S02E10.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDOUT] [+0:00:00]     DEBUG: Absolute MKV path: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E10.1080p.BluRay.x264-TAXES\Steven.Universe.S02E10.1080p.BluRay.x264-TAXES.mkv (170 chars)
[2025-09-18 09:13:10] [STDOUT] [+0:00:00] 
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,772 - pipeline_02 - INFO -      Long path handling completed - safe to proceed with detection
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,773 - pipeline_02 - INFO - 🔍 Checking for Windows 8.3 short name corruption...
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,774 - pipeline_02 - INFO -      No completed movies found in filesystem
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,774 - pipeline_02 - INFO -      ROBUST DETECTION: Scanning filesystem for completed downloads...
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,774 - pipeline_02 - INFO -      Scanning for completed downloads in: workspace\1_downloading\complete_raw
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,774 - pipeline_02 - INFO -      Content type filter: movie
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,774 - pipeline_02 - INFO -      ENHANCED DETECTION: Scanning for both movies and TV shows...
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,774 - pipeline_02 - INFO -      FILESYSTEM SCAN: Found 0 movies and 0 TV shows
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,774 - pipeline_02 - INFO -      TOTAL CONTENT: 0 items ready for processing
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,774 - pipeline_02 - INFO -      ORGANIZATION SUMMARY: 0 movies organized successfully
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,777 - pipeline_02 - INFO -      Radarr API: Retrieved 0 movies for status sync
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,778 - pipeline_02 - INFO - Pipeline state refreshed - found 0 movies
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,778 - pipeline_02 - INFO - ✅ Real-time telemetry system cleaned up
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,778 - pipeline_02 - INFO - ===== Finished Modern Radarr Download Monitoring =====
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,778 - pipeline_02 - INFO -     No new completed downloads found this run
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,778 - pipeline_02 - INFO - 📺 Starting Sonarr (TV Shows) monitoring...
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,778 - pipeline_02 - INFO - ===== Starting Modern Sonarr TV Show Download Monitoring with SQLite =====
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,778 - pipeline_02 - INFO -      ENHANCED: Dual-detection system (Filesystem + Sonarr API) + SQLite state
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,779 - _internal.utils.filesystem_first_state_manager - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,779 - pipeline_02 - INFO - Discovering TV shows by scanning filesystem...
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,780 - pipeline_02 - INFO - Found 0 content items across 14 stages
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,780 - pipeline_02 - INFO -      SABnzbd complete directory: workspace\1_downloading\complete_raw
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,780 - pipeline_02 - INFO -      Sonarr API endpoint: http://localhost:8989
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,780 - pipeline_02 - INFO -      SMART STATE VALIDATION: Checking for inconsistent TV show states...
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,783 - pipeline_02 - INFO - Retrieved 2 TV series from Sonarr
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,784 - pipeline_02 - INFO -      Active TV show downloads in Sonarr queue: 10
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,785 - pipeline_02 - INFO -      ENHANCED: Checking both Sonarr API and filesystem for completed TV shows
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,785 - pipeline_02 - INFO -      Will scan SABnzbd directory: workspace\1_downloading\complete_raw
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,785 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S01E46.720p.BluRay.x264-TAXES.mkv (0.21 GB)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,785 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S01E46.720p.BluRay.x264-TAXES (1 video files, ~0.21 GB)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,785 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E01.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,785 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E01.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,786 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E02.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,786 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E02.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,786 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E03.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,786 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E03.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,786 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E04.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,786 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E04.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,786 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E05.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,786 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E05.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,786 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E06.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,787 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E06.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,787 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E07.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,787 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E07.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,787 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E08.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,787 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E08.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,787 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E09.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,787 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E09.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,787 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E10.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,787 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E10.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,787 - pipeline_02 - INFO -      FILESYSTEM DETECTION: Found 11 completed TV folders ready for organization
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,787 - pipeline_02 - INFO -      Processing 11 completed TV folders
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,789 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S01E46.720p.BluRay.x264-TAXES, processing...
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,789 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S01E46.720p.BluRay.x264-TAXES
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,789 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,789 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S01E46.720p.BluRay.x264-TAXES
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,789 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '720p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S01E46.720p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,789 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,790 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): type object 'datetime.datetime' has no attribute 'datetime'
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,790 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S01E46.720p.BluRay.x264-TAXES'
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,793 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,793 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,793 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,793 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,793 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,822 - pipeline_02 - INFO - Detected resolution 1280x720 for Steven.Universe.S01E46.720p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,822 - pipeline_02 - INFO - 🎥 ffprobe detection: 720p (1280x720) from video file analysis
[2025-09-18 09:13:10] [STDERR] [+0:00:00] 2025-09-18 09:13:10,822 - pipeline_02 - INFO - 📊 Final resolution determination: 720p (width: 1280px)
[2025-09-18 09:13:11] [STDERR] [+0:00:01] 2025-09-18 09:13:11,991 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 09:13:11] [STDERR] [+0:00:01] 2025-09-18 09:13:11,993 - pipeline_02 - INFO - Created directory: workspace\2_downloaded_and_organized\tv_shows\720p\Steven Universe (2013)\Season 01
[2025-09-18 09:13:11] [STDERR] [+0:00:01] 2025-09-18 09:13:11,993 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S01E46.720p.BluRay.x264-TAXES\Steven.Universe.S01E46.720p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\720p\Steven Universe (2013)\Season 01\S01E46.mkv'
[2025-09-18 09:13:11] [STDERR] [+0:00:01] 2025-09-18 09:13:11,993 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 09:13:11] [STDERR] [+0:00:01] 2025-09-18 09:13:11,993 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S01E46.720p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 09:13:11] [STDERR] [+0:00:01] 2025-09-18 09:13:11,996 - pipeline_02 - INFO - 📝 Updated .organized - S01E46 with metadata
[2025-09-18 09:13:11] [STDERR] [+0:00:01] 2025-09-18 09:13:11,996 - pipeline_02 - INFO - ✅ Episode-aware marker set for S01E46
[2025-09-18 09:13:11] [STDERR] [+0:00:01] 2025-09-18 09:13:11,998 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S01E46.720p.BluRay.x264-TAXES
[2025-09-18 09:13:11] [STDERR] [+0:00:01] 2025-09-18 09:13:11,998 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S01E46.720p.BluRay.x264-TAXES
[2025-09-18 09:13:12] [STDERR] [+0:00:01] 2025-09-18 09:13:12,001 - pipeline_02 - INFO -    Checking 10 queue items for pending episodes...
[2025-09-18 09:13:12] [STDERR] [+0:00:01] 2025-09-18 09:13:12,001 - pipeline_02 - INFO -    Found 1 episodes still downloading/pending:
[2025-09-18 09:13:12] [STDERR] [+0:00:01] 2025-09-18 09:13:12,001 - pipeline_02 - INFO -       - Steven.Universe.S02E26.1080p.BluRay.x264-TAXES (Status: downloading)
[2025-09-18 09:13:12] [STDERR] [+0:00:01] 2025-09-18 09:13:12,001 - pipeline_02 - INFO -      ⏳ Other episodes still pending - skipping Sonarr cleanup
[2025-09-18 09:13:12] [STDERR] [+0:00:01] 2025-09-18 09:13:12,003 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E01.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 09:13:12] [STDERR] [+0:00:01] 2025-09-18 09:13:12,003 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E01.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:12] [STDERR] [+0:00:01] 2025-09-18 09:13:12,003 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 09:13:12] [STDERR] [+0:00:01] 2025-09-18 09:13:12,003 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E01.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:12] [STDERR] [+0:00:01] 2025-09-18 09:13:12,003 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E01.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 09:13:12] [STDERR] [+0:00:01] 2025-09-18 09:13:12,003 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 09:13:12] [STDERR] [+0:00:01] 2025-09-18 09:13:12,003 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): type object 'datetime.datetime' has no attribute 'datetime'
[2025-09-18 09:13:12] [STDERR] [+0:00:01] 2025-09-18 09:13:12,003 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S02E01.1080p.BluRay.x264-TAXES'
[2025-09-18 09:13:12] [STDERR] [+0:00:01] 2025-09-18 09:13:12,006 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 09:13:12] [STDERR] [+0:00:01] 2025-09-18 09:13:12,006 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 09:13:12] [STDERR] [+0:00:01] 2025-09-18 09:13:12,006 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 09:13:12] [STDERR] [+0:00:01] 2025-09-18 09:13:12,006 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 09:13:12] [STDERR] [+0:00:01] 2025-09-18 09:13:12,006 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 09:13:12] [STDERR] [+0:00:01] 2025-09-18 09:13:12,037 - pipeline_02 - INFO - Detected resolution 1920x1080 for Steven.Universe.S02E01.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:12] [STDERR] [+0:00:01] 2025-09-18 09:13:12,038 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 09:13:12] [STDERR] [+0:00:01] 2025-09-18 09:13:12,038 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,524 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,525 - pipeline_02 - INFO - Created directory: workspace\2_downloaded_and_organized\tv_shows\1080p\Steven Universe (2013)\Season 02
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,526 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S02E01.1080p.BluRay.x264-TAXES\Steven.Universe.S02E01.1080p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Steven Universe (2013)\Season 02\S02E01.mkv'
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,526 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,526 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S02E01.1080p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,526 - pipeline_02 - INFO - 📝 Updated .organized - S02E01 with metadata
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,527 - pipeline_02 - INFO - ✅ Episode-aware marker set for S02E01
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,527 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E01.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,527 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S02E01.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,530 - pipeline_02 - INFO -    Checking 10 queue items for pending episodes...
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,530 - pipeline_02 - INFO -    Found 1 episodes still downloading/pending:
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,530 - pipeline_02 - INFO -       - Steven.Universe.S02E26.1080p.BluRay.x264-TAXES (Status: downloading)
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,531 - pipeline_02 - INFO -      ⏳ Other episodes still pending - skipping Sonarr cleanup
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,531 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E02.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,531 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E02.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,531 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,531 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E02.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,531 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E02.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,532 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,532 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): type object 'datetime.datetime' has no attribute 'datetime'
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,532 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S02E02.1080p.BluRay.x264-TAXES'
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,536 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,537 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,537 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,537 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,537 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,563 - pipeline_02 - INFO - Detected resolution 1920x1080 for Steven.Universe.S02E02.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,563 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 09:13:13] [STDERR] [+0:00:02] 2025-09-18 09:13:13,563 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,426 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,427 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S02E02.1080p.BluRay.x264-TAXES\Steven.Universe.S02E02.1080p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Steven Universe (2013)\Season 02\S02E02.mkv'
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,427 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,428 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S02E02.1080p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,428 - pipeline_02 - INFO - 📝 Updated .organized - S02E02 with metadata
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,428 - pipeline_02 - INFO - ✅ Episode-aware marker set for S02E02
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,429 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E02.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,429 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S02E02.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,431 - pipeline_02 - INFO -    Checking 10 queue items for pending episodes...
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,431 - pipeline_02 - INFO -    Found 1 episodes still downloading/pending:
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,431 - pipeline_02 - INFO -       - Steven.Universe.S02E26.1080p.BluRay.x264-TAXES (Status: downloading)
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,431 - pipeline_02 - INFO -      ⏳ Other episodes still pending - skipping Sonarr cleanup
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,433 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E03.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,433 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E03.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,433 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,433 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E03.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,433 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E03.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,433 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,433 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): type object 'datetime.datetime' has no attribute 'datetime'
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,433 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S02E03.1080p.BluRay.x264-TAXES'
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,436 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,436 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,436 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,436 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,436 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,463 - pipeline_02 - INFO - Detected resolution 1920x1080 for Steven.Universe.S02E03.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,463 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 09:13:14] [STDERR] [+0:00:03] 2025-09-18 09:13:14,463 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,404 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,405 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S02E03.1080p.BluRay.x264-TAXES\Steven.Universe.S02E03.1080p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Steven Universe (2013)\Season 02\S02E03.mkv'
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,406 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,406 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S02E03.1080p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,406 - pipeline_02 - INFO - 📝 Updated .organized - S02E03 with metadata
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,406 - pipeline_02 - INFO - ✅ Episode-aware marker set for S02E03
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,407 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E03.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,407 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S02E03.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,410 - pipeline_02 - INFO -    Checking 10 queue items for pending episodes...
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,410 - pipeline_02 - INFO -    Found 1 episodes still downloading/pending:
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,410 - pipeline_02 - INFO -       - Steven.Universe.S02E26.1080p.BluRay.x264-TAXES (Status: downloading)
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,410 - pipeline_02 - INFO -      ⏳ Other episodes still pending - skipping Sonarr cleanup
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,412 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E04.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,412 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E04.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,412 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,412 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E04.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,412 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E04.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,412 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,412 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): type object 'datetime.datetime' has no attribute 'datetime'
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,412 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S02E04.1080p.BluRay.x264-TAXES'
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,415 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,415 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,415 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,415 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,415 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,442 - pipeline_02 - INFO - Detected resolution 1920x1080 for Steven.Universe.S02E04.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,442 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 09:13:15] [STDERR] [+0:00:04] 2025-09-18 09:13:15,442 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,492 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,495 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S02E04.1080p.BluRay.x264-TAXES\Steven.Universe.S02E04.1080p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Steven Universe (2013)\Season 02\S02E04.mkv'
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,495 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,495 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S02E04.1080p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,496 - pipeline_02 - INFO - 📝 Updated .organized - S02E04 with metadata
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,496 - pipeline_02 - INFO - ✅ Episode-aware marker set for S02E04
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,497 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E04.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,497 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S02E04.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,499 - pipeline_02 - INFO -    Checking 10 queue items for pending episodes...
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,499 - pipeline_02 - INFO -    Found 1 episodes still downloading/pending:
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,499 - pipeline_02 - INFO -       - Steven.Universe.S02E26.1080p.BluRay.x264-TAXES (Status: downloading)
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,499 - pipeline_02 - INFO -      ⏳ Other episodes still pending - skipping Sonarr cleanup
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,500 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E05.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,500 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E05.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,500 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,500 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E05.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,500 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E05.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,500 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,500 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): type object 'datetime.datetime' has no attribute 'datetime'
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,500 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S02E05.1080p.BluRay.x264-TAXES'
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,504 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,504 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,504 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,504 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,504 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,532 - pipeline_02 - INFO - Detected resolution 1920x1080 for Steven.Universe.S02E05.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,532 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 09:13:16] [STDERR] [+0:00:05] 2025-09-18 09:13:16,532 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,626 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,627 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S02E05.1080p.BluRay.x264-TAXES\Steven.Universe.S02E05.1080p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Steven Universe (2013)\Season 02\S02E05.mkv'
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,627 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,628 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S02E05.1080p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,628 - pipeline_02 - INFO - 📝 Updated .organized - S02E05 with metadata
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,628 - pipeline_02 - INFO - ✅ Episode-aware marker set for S02E05
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,629 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E05.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,629 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S02E05.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,632 - pipeline_02 - INFO -    Checking 10 queue items for pending episodes...
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,632 - pipeline_02 - INFO -    Found 1 episodes still downloading/pending:
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,632 - pipeline_02 - INFO -       - Steven.Universe.S02E26.1080p.BluRay.x264-TAXES (Status: downloading)
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,632 - pipeline_02 - INFO -      ⏳ Other episodes still pending - skipping Sonarr cleanup
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,633 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E06.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,633 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E06.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,633 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,634 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E06.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,634 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E06.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,634 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,634 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): type object 'datetime.datetime' has no attribute 'datetime'
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,634 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S02E06.1080p.BluRay.x264-TAXES'
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,637 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,637 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,637 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,637 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,637 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,663 - pipeline_02 - INFO - Detected resolution 1920x1080 for Steven.Universe.S02E06.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,663 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 09:13:17] [STDERR] [+0:00:06] 2025-09-18 09:13:17,664 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,681 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,681 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S02E06.1080p.BluRay.x264-TAXES\Steven.Universe.S02E06.1080p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Steven Universe (2013)\Season 02\S02E06.mkv'
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,682 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,682 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S02E06.1080p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,682 - pipeline_02 - INFO - 📝 Updated .organized - S02E06 with metadata
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,682 - pipeline_02 - INFO - ✅ Episode-aware marker set for S02E06
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,683 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E06.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,683 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S02E06.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,686 - pipeline_02 - INFO -    Checking 10 queue items for pending episodes...
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,686 - pipeline_02 - INFO -    Found 1 episodes still downloading/pending:
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,686 - pipeline_02 - INFO -       - Steven.Universe.S02E26.1080p.BluRay.x264-TAXES (Status: downloading)
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,686 - pipeline_02 - INFO -      ⏳ Other episodes still pending - skipping Sonarr cleanup
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,687 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E07.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,687 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E07.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,687 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,687 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E07.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,687 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E07.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,687 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,687 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): type object 'datetime.datetime' has no attribute 'datetime'
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,687 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S02E07.1080p.BluRay.x264-TAXES'
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,691 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,691 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,691 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,691 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,691 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,718 - pipeline_02 - INFO - Detected resolution 1920x1080 for Steven.Universe.S02E07.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,718 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 09:13:18] [STDERR] [+0:00:07] 2025-09-18 09:13:18,718 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 09:13:19] [STDERR] [+0:00:08] 2025-09-18 09:13:19,711 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 09:13:19] [STDERR] [+0:00:08] 2025-09-18 09:13:19,712 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S02E07.1080p.BluRay.x264-TAXES\Steven.Universe.S02E07.1080p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Steven Universe (2013)\Season 02\S02E07.mkv'
[2025-09-18 09:13:19] [STDERR] [+0:00:08] 2025-09-18 09:13:19,712 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 09:13:19] [STDERR] [+0:00:08] 2025-09-18 09:13:19,713 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S02E07.1080p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 09:13:19] [STDERR] [+0:00:08] 2025-09-18 09:13:19,713 - pipeline_02 - INFO - 📝 Updated .organized - S02E07 with metadata
[2025-09-18 09:13:19] [STDERR] [+0:00:08] 2025-09-18 09:13:19,713 - pipeline_02 - INFO - ✅ Episode-aware marker set for S02E07
[2025-09-18 09:13:19] [STDERR] [+0:00:08] 2025-09-18 09:13:19,714 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E07.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:19] [STDERR] [+0:00:08] 2025-09-18 09:13:19,714 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S02E07.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:19] [STDERR] [+0:00:08] 2025-09-18 09:13:19,716 - pipeline_02 - INFO -    Checking 10 queue items for pending episodes...
[2025-09-18 09:13:19] [STDERR] [+0:00:08] 2025-09-18 09:13:19,716 - pipeline_02 - INFO -    Found 1 episodes still downloading/pending:
[2025-09-18 09:13:19] [STDERR] [+0:00:08] 2025-09-18 09:13:19,717 - pipeline_02 - INFO -       - Steven.Universe.S02E26.1080p.BluRay.x264-TAXES (Status: downloading)
[2025-09-18 09:13:19] [STDERR] [+0:00:08] 2025-09-18 09:13:19,717 - pipeline_02 - INFO -      ⏳ Other episodes still pending - skipping Sonarr cleanup
[2025-09-18 09:13:19] [STDERR] [+0:00:08] 2025-09-18 09:13:19,718 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E08.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 09:13:19] [STDERR] [+0:00:08] 2025-09-18 09:13:19,718 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E08.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:19] [STDERR] [+0:00:08] 2025-09-18 09:13:19,718 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 09:13:19] [STDERR] [+0:00:08] 2025-09-18 09:13:19,718 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E08.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:19] [STDERR] [+0:00:08] 2025-09-18 09:13:19,718 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E08.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 09:13:19] [STDERR] [+0:00:08] 2025-09-18 09:13:19,718 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 09:13:19] [STDERR] [+0:00:08] 2025-09-18 09:13:19,718 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): type object 'datetime.datetime' has no attribute 'datetime'
[2025-09-18 09:13:19] [STDERR] [+0:00:08] 2025-09-18 09:13:19,719 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S02E08.1080p.BluRay.x264-TAXES'
[2025-09-18 09:13:19] [STDERR] [+0:00:08] 2025-09-18 09:13:19,722 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 09:13:19] [STDERR] [+0:00:08] 2025-09-18 09:13:19,722 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 09:13:19] [STDERR] [+0:00:08] 2025-09-18 09:13:19,722 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 09:13:19] [STDERR] [+0:00:08] 2025-09-18 09:13:19,722 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 09:13:19] [STDERR] [+0:00:08] 2025-09-18 09:13:19,722 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 09:13:19] [STDERR] [+0:00:09] 2025-09-18 09:13:19,749 - pipeline_02 - INFO - Detected resolution 1920x1080 for Steven.Universe.S02E08.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:19] [STDERR] [+0:00:09] 2025-09-18 09:13:19,749 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 09:13:19] [STDERR] [+0:00:09] 2025-09-18 09:13:19,749 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,611 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,612 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S02E08.1080p.BluRay.x264-TAXES\Steven.Universe.S02E08.1080p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Steven Universe (2013)\Season 02\S02E08.mkv'
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,612 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,612 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S02E08.1080p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,613 - pipeline_02 - INFO - 📝 Updated .organized - S02E08 with metadata
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,613 - pipeline_02 - INFO - ✅ Episode-aware marker set for S02E08
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,613 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E08.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,613 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S02E08.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,616 - pipeline_02 - INFO -    Checking 10 queue items for pending episodes...
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,616 - pipeline_02 - INFO -    Found 1 episodes still downloading/pending:
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,616 - pipeline_02 - INFO -       - Steven.Universe.S02E26.1080p.BluRay.x264-TAXES (Status: downloading)
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,616 - pipeline_02 - INFO -      ⏳ Other episodes still pending - skipping Sonarr cleanup
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,617 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E09.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,617 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E09.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,617 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,618 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E09.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,618 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E09.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,618 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,618 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): type object 'datetime.datetime' has no attribute 'datetime'
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,618 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S02E09.1080p.BluRay.x264-TAXES'
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,621 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,621 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,621 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,621 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,621 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,648 - pipeline_02 - INFO - Detected resolution 1920x1080 for Steven.Universe.S02E09.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,648 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 09:13:20] [STDERR] [+0:00:09] 2025-09-18 09:13:20,648 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,547 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,548 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S02E09.1080p.BluRay.x264-TAXES\Steven.Universe.S02E09.1080p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Steven Universe (2013)\Season 02\S02E09.mkv'
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,548 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,548 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S02E09.1080p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,549 - pipeline_02 - INFO - 📝 Updated .organized - S02E09 with metadata
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,549 - pipeline_02 - INFO - ✅ Episode-aware marker set for S02E09
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,549 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E09.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,550 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S02E09.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,552 - pipeline_02 - INFO -    Checking 10 queue items for pending episodes...
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,552 - pipeline_02 - INFO -    Found 1 episodes still downloading/pending:
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,553 - pipeline_02 - INFO -       - Steven.Universe.S02E26.1080p.BluRay.x264-TAXES (Status: downloading)
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,553 - pipeline_02 - INFO -      ⏳ Other episodes still pending - skipping Sonarr cleanup
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,553 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E10.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,553 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E10.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,553 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,553 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E10.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,553 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E10.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,553 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,553 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): type object 'datetime.datetime' has no attribute 'datetime'
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,554 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S02E10.1080p.BluRay.x264-TAXES'
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,558 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,558 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,558 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,559 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,559 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,585 - pipeline_02 - INFO - Detected resolution 1920x1080 for Steven.Universe.S02E10.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,585 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 09:13:21] [STDERR] [+0:00:10] 2025-09-18 09:13:21,585 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 09:13:22] [STDERR] [+0:00:11] 2025-09-18 09:13:22,553 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 09:13:22] [STDERR] [+0:00:11] 2025-09-18 09:13:22,555 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S02E10.1080p.BluRay.x264-TAXES\Steven.Universe.S02E10.1080p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Steven Universe (2013)\Season 02\S02E10.mkv'
[2025-09-18 09:13:22] [STDERR] [+0:00:11] 2025-09-18 09:13:22,555 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 09:13:22] [STDERR] [+0:00:11] 2025-09-18 09:13:22,555 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S02E10.1080p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 09:13:22] [STDERR] [+0:00:11] 2025-09-18 09:13:22,556 - pipeline_02 - INFO - 📝 Updated .organized - S02E10 with metadata
[2025-09-18 09:13:22] [STDERR] [+0:00:11] 2025-09-18 09:13:22,556 - pipeline_02 - INFO - ✅ Episode-aware marker set for S02E10
[2025-09-18 09:13:22] [STDERR] [+0:00:11] 2025-09-18 09:13:22,556 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E10.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:22] [STDERR] [+0:00:11] 2025-09-18 09:13:22,556 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S02E10.1080p.BluRay.x264-TAXES
[2025-09-18 09:13:22] [STDERR] [+0:00:11] 2025-09-18 09:13:22,559 - pipeline_02 - INFO -    Checking 10 queue items for pending episodes...
[2025-09-18 09:13:22] [STDERR] [+0:00:11] 2025-09-18 09:13:22,559 - pipeline_02 - INFO -    Found 1 episodes still downloading/pending:
[2025-09-18 09:13:22] [STDERR] [+0:00:11] 2025-09-18 09:13:22,559 - pipeline_02 - INFO -       - Steven.Universe.S02E26.1080p.BluRay.x264-TAXES (Status: downloading)
[2025-09-18 09:13:22] [STDERR] [+0:00:11] 2025-09-18 09:13:22,559 - pipeline_02 - INFO -      ⏳ Other episodes still pending - skipping Sonarr cleanup
[2025-09-18 09:13:22] [STDERR] [+0:00:11] 2025-09-18 09:13:22,559 - pipeline_02 - INFO - 🔄 Checking for dynamic season progression opportunities...
[2025-09-18 09:13:22] [STDERR] [+0:00:11] 2025-09-18 09:13:22,559 - pipeline_02 - INFO -      Sequential progression enabled for 0 series
[2025-09-18 09:13:22] [STDERR] [+0:00:11] 2025-09-18 09:13:22,559 - pipeline_02 - INFO -      No series opted-in for sequential progression
[2025-09-18 09:13:22] [STDERR] [+0:00:11] 2025-09-18 09:13:22,559 - pipeline_02 - INFO - ===== Finished Modern Sonarr TV Show Download Monitoring =====
[2025-09-18 09:13:22] [STDERR] [+0:00:11] 2025-09-18 09:13:22,559 - pipeline_02 - INFO -     ✅ Pipeline 02 completed successfully (Movies + TV Shows)
[2025-09-18 09:13:22] [STDERR] [+0:00:11] 2025-09-18 09:13:22,559 - pipeline_02 - INFO - ===== Finished Pipeline 02 Execution =====
[2025-09-18 09:13:22] [STDOUT] [+0:00:11] ------------------------------------------------------------
[2025-09-18 09:13:22] [STDOUT] [+0:00:11] 
[2025-09-18 09:13:22] [STDOUT] [+0:00:11] 🏁 Terminal logging ended for 02_download_and_organize
[2025-09-18 09:13:22] [STDOUT] [+0:00:11] 
[2025-09-18 09:13:22] [STDOUT] [+0:00:11] 🕐 Ended at: 2025-09-18 09:13:22
[2025-09-18 09:13:22] [STDOUT] [+0:00:11] 
[2025-09-18 09:13:22] [STDOUT] [+0:00:11] ⏱️ Total duration: 0:00:11.818491
[2025-09-18 09:13:22] [STDOUT] [+0:00:11] 
[2025-09-18 09:13:22] [STDOUT] [+0:00:11] 📄 Log saved to: C:\Users\<USER>\Videos\PlexAutomator\logs\02_download_and_organize_2025-09-18_09-13-10-AM.txt
[2025-09-18 09:13:22] [STDOUT] [+0:00:11] 


==================================================
=== TERMINAL OUTPUT LOG END ===
Script: 02_download_and_organize
Ended: 2025-09-18 09:13:22
Duration: 0:00:11.818491
==================================================
