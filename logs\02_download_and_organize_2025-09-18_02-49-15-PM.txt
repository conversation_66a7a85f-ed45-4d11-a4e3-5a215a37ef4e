=== TERMINAL OUTPUT LOG ===
Script: 02_download_and_organize
Started: 2025-09-18 14:49:15
Log File: C:\Users\<USER>\Videos\PlexAutomator\logs\02_download_and_organize_2025-09-18_02-49-15-PM.txt
==================================================

[2025-09-18 14:49:15] [STDOUT] [+0:00:00] 📝 Terminal logging started for 02_download_and_organize
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] 
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] 📄 Log file: C:\Users\<USER>\Videos\PlexAutomator\logs\02_download_and_organize_2025-09-18_02-49-15-PM.txt
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] 
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] 🕐 Started at: 2025-09-18 14:49:15
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] 
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] ------------------------------------------------------------
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] 
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] *** UNIFIED Stage 02: Download and Organize ***
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] 
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] ==================================================
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] 
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] + Consolidated from multiple O2 scripts into one unified implementation
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] 
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] >> Modern Radarr API integration
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] 
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] -- Simplified workflow: Radarr -> SABnzbd -> Plex
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] 
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] >> Clean, maintainable codebase
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] 
[2025-09-18 14:49:15] [STDOUT] [+0:00:00]    Default: Interactive mode (use --movies-only, --tv-only, or --all for command-line mode)
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] 
[2025-09-18 14:49:15] [STDERR] [+0:00:00] 2025-09-18 14:49:15,953 - pipeline_02 - INFO - ===== Starting Pipeline 02 Execution =====
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] 
[2025-09-18 14:49:15] [STDERR] [+0:00:00] 2025-09-18 14:49:15,955 - pipeline_02 - INFO - Settings loaded successfully
[2025-09-18 14:49:15] [STDERR] [+0:00:00] 2025-09-18 14:49:15,955 - pipeline_02 - INFO - Command-line mode: Processing tv_shows
[2025-09-18 14:49:15] [STDERR] [+0:00:00] 2025-09-18 14:49:15,955 - pipeline_02 - INFO - 📺 Starting Sonarr (TV Shows) monitoring...
[2025-09-18 14:49:15] [STDERR] [+0:00:00] 2025-09-18 14:49:15,955 - pipeline_02 - INFO - ===== Starting Modern Sonarr TV Show Download Monitoring with SQLite =====
[2025-09-18 14:49:15] [STDERR] [+0:00:00] 2025-09-18 14:49:15,955 - pipeline_02 - INFO -      ENHANCED: Dual-detection system (Filesystem + Sonarr API) + SQLite state
[2025-09-18 14:49:15] [STDERR] [+0:00:00] 2025-09-18 14:49:15,956 - _internal.utils.filesystem_first_state_manager - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db
[2025-09-18 14:49:15] [STDERR] [+0:00:00] 2025-09-18 14:49:15,956 - pipeline_02 - INFO - Discovering TV shows by scanning filesystem...
[2025-09-18 14:49:15] [STDERR] [+0:00:00] 2025-09-18 14:49:15,958 - pipeline_02 - INFO - Found 0 content items across 14 stages
[2025-09-18 14:49:15] [STDERR] [+0:00:00] 2025-09-18 14:49:15,958 - pipeline_02 - INFO -      SABnzbd complete directory: workspace\1_downloading\complete_raw
[2025-09-18 14:49:15] [STDERR] [+0:00:00] 2025-09-18 14:49:15,958 - pipeline_02 - INFO -      Sonarr API endpoint: http://localhost:8989
[2025-09-18 14:49:15] [STDERR] [+0:00:00] 2025-09-18 14:49:15,958 - pipeline_02 - INFO -      SMART STATE VALIDATION: Checking for inconsistent TV show states...
[2025-09-18 14:49:15] [STDERR] [+0:00:00] 2025-09-18 14:49:15,967 - pipeline_02 - INFO - Retrieved 2 TV series from Sonarr
[2025-09-18 14:49:15] [STDERR] [+0:00:00] 2025-09-18 14:49:15,969 - pipeline_02 - INFO -      Active TV show downloads in Sonarr queue: 6
[2025-09-18 14:49:15] [STDERR] [+0:00:00] 2025-09-18 14:49:15,969 - pipeline_02 - INFO -      ENHANCED: Checking both Sonarr API and filesystem for completed TV shows
[2025-09-18 14:49:15] [STDERR] [+0:00:00] 2025-09-18 14:49:15,969 - pipeline_02 - INFO -      Will scan SABnzbd directory: workspace\1_downloading\complete_raw
[2025-09-18 14:49:15] [STDERR] [+0:00:00] 2025-09-18 14:49:15,970 - pipeline_02 - INFO -      No completed TV show folders found in filesystem
[2025-09-18 14:49:15] [STDERR] [+0:00:00] 2025-09-18 14:49:15,970 - pipeline_02 - INFO -      No completed TV shows found for organization
[2025-09-18 14:49:15] [STDERR] [+0:00:00] 2025-09-18 14:49:15,970 - pipeline_02 - INFO - 🔄 Checking for dynamic season progression opportunities...
[2025-09-18 14:49:15] [STDERR] [+0:00:00] 2025-09-18 14:49:15,970 - pipeline_02 - INFO -      Sequential progression enabled for 0 series
[2025-09-18 14:49:15] [STDERR] [+0:00:00] 2025-09-18 14:49:15,970 - pipeline_02 - INFO -      No series opted-in for sequential progression
[2025-09-18 14:49:15] [STDERR] [+0:00:00] 2025-09-18 14:49:15,970 - pipeline_02 - INFO - ===== Finished Modern Sonarr TV Show Download Monitoring =====
[2025-09-18 14:49:15] [STDERR] [+0:00:00] 2025-09-18 14:49:15,970 - pipeline_02 - INFO -     ✅ Pipeline 02 completed successfully (TV Shows)
[2025-09-18 14:49:15] [STDERR] [+0:00:00] 2025-09-18 14:49:15,970 - pipeline_02 - INFO - ===== Finished Pipeline 02 Execution =====
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] ------------------------------------------------------------
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] 
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] 🏁 Terminal logging ended for 02_download_and_organize
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] 
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] 🕐 Ended at: 2025-09-18 14:49:15
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] 
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] ⏱️ Total duration: 0:00:00.022399
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] 
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] 📄 Log saved to: C:\Users\<USER>\Videos\PlexAutomator\logs\02_download_and_organize_2025-09-18_02-49-15-PM.txt
[2025-09-18 14:49:15] [STDOUT] [+0:00:00] 


==================================================
=== TERMINAL OUTPUT LOG END ===
Script: 02_download_and_organize
Ended: 2025-09-18 14:49:15
Duration: 0:00:00.022399
==================================================
