2025-09-18 09:11:18,282 - INFO - ============================================================
2025-09-18 09:11:18,282 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:11:18,282 - INFO - Timestamp: 2025-09-18T13:11:18.282537+00:00
2025-09-18 09:11:18,282 - INFO - ============================================================
2025-09-18 09:11:18,282 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:11:18,282 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E01.1080p.BluRay.x264-TAXES
2025-09-18 09:11:18,323 - INFO -   %2 (original_nzb_name): Steven.Universe.S02E01.1080p.BluRay.x264-TAXES.nzb
2025-09-18 09:11:18,323 - INFO -   %3 (clean_job_name): Steven.Universe.S02E01.1080p.BluRay.x264-TAXES
2025-09-18 09:11:18,323 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:11:18,323 - INFO -   %5 (category): tv
2025-09-18 09:11:18,323 - INFO -   %6 (group): alt.binaries.superman
2025-09-18 09:11:18,323 - INFO -   %7 (post_process_status): 0
2025-09-18 09:11:18,323 - INFO - ========================================
2025-09-18 09:11:18,323 - INFO - Download completed successfully: Steven.Universe.S02E01.1080p.BluRay.x264-TAXES
2025-09-18 09:11:18,323 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E01.1080p.BluRay.x264-TAXES
2025-09-18 09:11:18,323 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:11:18,323 - ERROR - Post-processing failed
2025-09-18 09:11:33,336 - INFO - ============================================================
2025-09-18 09:11:33,337 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:11:33,337 - INFO - Timestamp: 2025-09-18T13:11:33.337066+00:00
2025-09-18 09:11:33,337 - INFO - ============================================================
2025-09-18 09:11:33,337 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:11:33,337 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E02.1080p.BluRay.x264-TAXES
2025-09-18 09:11:33,337 - INFO -   %2 (original_nzb_name): Steven.Universe.S02E02.1080p.BluRay.x264-TAXES.nzb
2025-09-18 09:11:33,337 - INFO -   %3 (clean_job_name): Steven.Universe.S02E02.1080p.BluRay.x264-TAXES
2025-09-18 09:11:33,337 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:11:33,337 - INFO -   %5 (category): tv
2025-09-18 09:11:33,337 - INFO -   %6 (group): alt.binaries.xylo
2025-09-18 09:11:33,337 - INFO -   %7 (post_process_status): 0
2025-09-18 09:11:33,337 - INFO - ========================================
2025-09-18 09:11:33,337 - INFO - Download completed successfully: Steven.Universe.S02E02.1080p.BluRay.x264-TAXES
2025-09-18 09:11:33,337 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E02.1080p.BluRay.x264-TAXES
2025-09-18 09:11:33,337 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:11:33,337 - ERROR - Post-processing failed
2025-09-18 09:11:42,307 - INFO - ============================================================
2025-09-18 09:11:42,307 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:11:42,307 - INFO - Timestamp: 2025-09-18T13:11:42.307256+00:00
2025-09-18 09:11:42,307 - INFO - ============================================================
2025-09-18 09:11:42,307 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:11:42,307 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E03.1080p.BluRay.x264-TAXES
2025-09-18 09:11:42,307 - INFO -   %2 (original_nzb_name): Steven.Universe.S02E03.1080p.BluRay.x264-TAXES.nzb
2025-09-18 09:11:42,307 - INFO -   %3 (clean_job_name): Steven.Universe.S02E03.1080p.BluRay.x264-TAXES
2025-09-18 09:11:42,307 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:11:42,307 - INFO -   %5 (category): tv
2025-09-18 09:11:42,307 - INFO -   %6 (group): alt.binaries.xylo
2025-09-18 09:11:42,307 - INFO -   %7 (post_process_status): 0
2025-09-18 09:11:42,307 - INFO - ========================================
2025-09-18 09:11:42,307 - INFO - Download completed successfully: Steven.Universe.S02E03.1080p.BluRay.x264-TAXES
2025-09-18 09:11:42,307 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E03.1080p.BluRay.x264-TAXES
2025-09-18 09:11:42,307 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:11:42,307 - ERROR - Post-processing failed
2025-09-18 09:11:59,200 - INFO - ============================================================
2025-09-18 09:11:59,201 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:11:59,201 - INFO - Timestamp: 2025-09-18T13:11:59.201048+00:00
2025-09-18 09:11:59,201 - INFO - ============================================================
2025-09-18 09:11:59,201 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:11:59,201 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E04.1080p.BluRay.x264-TAXES
2025-09-18 09:11:59,201 - INFO -   %2 (original_nzb_name): Steven.Universe.S02E04.1080p.BluRay.x264-TAXES.nzb
2025-09-18 09:11:59,201 - INFO -   %3 (clean_job_name): Steven.Universe.S02E04.1080p.BluRay.x264-TAXES
2025-09-18 09:11:59,201 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:11:59,201 - INFO -   %5 (category): tv
2025-09-18 09:11:59,201 - INFO -   %6 (group): alt.binaries.goonies
2025-09-18 09:11:59,201 - INFO -   %7 (post_process_status): 0
2025-09-18 09:11:59,201 - INFO - ========================================
2025-09-18 09:11:59,201 - INFO - Download completed successfully: Steven.Universe.S02E04.1080p.BluRay.x264-TAXES
2025-09-18 09:11:59,201 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E04.1080p.BluRay.x264-TAXES
2025-09-18 09:11:59,201 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:11:59,201 - ERROR - Post-processing failed
2025-09-18 09:12:08,535 - INFO - ============================================================
2025-09-18 09:12:08,536 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:12:08,536 - INFO - Timestamp: 2025-09-18T13:12:08.536126+00:00
2025-09-18 09:12:08,536 - INFO - ============================================================
2025-09-18 09:12:08,536 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:12:08,536 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E05.1080p.BluRay.x264-TAXES
2025-09-18 09:12:08,536 - INFO -   %2 (original_nzb_name): Steven.Universe.S02E05.1080p.BluRay.x264-TAXES.nzb
2025-09-18 09:12:08,536 - INFO -   %3 (clean_job_name): Steven.Universe.S02E05.1080p.BluRay.x264-TAXES
2025-09-18 09:12:08,536 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:12:08,536 - INFO -   %5 (category): tv
2025-09-18 09:12:08,536 - INFO -   %6 (group): alt.binaries.goonies
2025-09-18 09:12:08,536 - INFO -   %7 (post_process_status): 0
2025-09-18 09:12:08,536 - INFO - ========================================
2025-09-18 09:12:08,536 - INFO - Download completed successfully: Steven.Universe.S02E05.1080p.BluRay.x264-TAXES
2025-09-18 09:12:08,536 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E05.1080p.BluRay.x264-TAXES
2025-09-18 09:12:08,536 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:12:08,536 - ERROR - Post-processing failed
2025-09-18 09:12:19,022 - INFO - ============================================================
2025-09-18 09:12:19,023 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:12:19,023 - INFO - Timestamp: 2025-09-18T13:12:19.023114+00:00
2025-09-18 09:12:19,023 - INFO - ============================================================
2025-09-18 09:12:19,023 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:12:19,023 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E06.1080p.BluRay.x264-TAXES
2025-09-18 09:12:19,023 - INFO -   %2 (original_nzb_name): Steven.Universe.S02E06.1080p.BluRay.x264-TAXES.nzb
2025-09-18 09:12:19,023 - INFO -   %3 (clean_job_name): Steven.Universe.S02E06.1080p.BluRay.x264-TAXES
2025-09-18 09:12:19,023 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:12:19,023 - INFO -   %5 (category): tv
2025-09-18 09:12:19,023 - INFO -   %6 (group): alt.binaries.goat
2025-09-18 09:12:19,023 - INFO -   %7 (post_process_status): 0
2025-09-18 09:12:19,023 - INFO - ========================================
2025-09-18 09:12:19,023 - INFO - Download completed successfully: Steven.Universe.S02E06.1080p.BluRay.x264-TAXES
2025-09-18 09:12:19,023 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E06.1080p.BluRay.x264-TAXES
2025-09-18 09:12:19,023 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:12:19,023 - ERROR - Post-processing failed
2025-09-18 09:12:29,782 - INFO - ============================================================
2025-09-18 09:12:29,782 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:12:29,782 - INFO - Timestamp: 2025-09-18T13:12:29.782710+00:00
2025-09-18 09:12:29,782 - INFO - ============================================================
2025-09-18 09:12:29,782 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:12:29,782 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E07.1080p.BluRay.x264-TAXES
2025-09-18 09:12:29,782 - INFO -   %2 (original_nzb_name): Steven.Universe.S02E07.1080p.BluRay.x264-TAXES.nzb
2025-09-18 09:12:29,782 - INFO -   %3 (clean_job_name): Steven.Universe.S02E07.1080p.BluRay.x264-TAXES
2025-09-18 09:12:29,782 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:12:29,782 - INFO -   %5 (category): tv
2025-09-18 09:12:29,782 - INFO -   %6 (group): alt.binaries.goat
2025-09-18 09:12:29,782 - INFO -   %7 (post_process_status): 0
2025-09-18 09:12:29,782 - INFO - ========================================
2025-09-18 09:12:29,782 - INFO - Download completed successfully: Steven.Universe.S02E07.1080p.BluRay.x264-TAXES
2025-09-18 09:12:29,783 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E07.1080p.BluRay.x264-TAXES
2025-09-18 09:12:29,783 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:12:29,783 - ERROR - Post-processing failed
2025-09-18 09:12:34,429 - INFO - ============================================================
2025-09-18 09:12:34,429 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:12:34,430 - INFO - Timestamp: 2025-09-18T13:12:34.429995+00:00
2025-09-18 09:12:34,430 - INFO - ============================================================
2025-09-18 09:12:34,430 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:12:34,430 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S01E46.720p.BluRay.x264-TAXES
2025-09-18 09:12:34,430 - INFO -   %2 (original_nzb_name): Steven.Universe.S01E46.720p.BluRay.x264-TAXES.nzb
2025-09-18 09:12:34,430 - INFO -   %3 (clean_job_name): Steven.Universe.S01E46.720p.BluRay.x264-TAXES
2025-09-18 09:12:34,430 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:12:34,430 - INFO -   %5 (category): tv
2025-09-18 09:12:34,430 - INFO -   %6 (group): alt.binaries.superman
2025-09-18 09:12:34,430 - INFO -   %7 (post_process_status): 0
2025-09-18 09:12:34,430 - INFO - ========================================
2025-09-18 09:12:34,430 - INFO - Download completed successfully: Steven.Universe.S01E46.720p.BluRay.x264-TAXES
2025-09-18 09:12:34,430 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S01E46.720p.BluRay.x264-TAXES
2025-09-18 09:12:34,430 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:12:34,430 - ERROR - Post-processing failed
2025-09-18 09:12:44,630 - INFO - ============================================================
2025-09-18 09:12:44,630 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:12:44,630 - INFO - Timestamp: 2025-09-18T13:12:44.630556+00:00
2025-09-18 09:12:44,630 - INFO - ============================================================
2025-09-18 09:12:44,630 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:12:44,630 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E08.1080p.BluRay.x264-TAXES
2025-09-18 09:12:44,630 - INFO -   %2 (original_nzb_name): Steven.Universe.S02E08.1080p.BluRay.x264-TAXES.nzb
2025-09-18 09:12:44,630 - INFO -   %3 (clean_job_name): Steven.Universe.S02E08.1080p.BluRay.x264-TAXES
2025-09-18 09:12:44,630 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:12:44,630 - INFO -   %5 (category): tv
2025-09-18 09:12:44,630 - INFO -   %6 (group): alt.binaries.flowed
2025-09-18 09:12:44,630 - INFO -   %7 (post_process_status): 0
2025-09-18 09:12:44,630 - INFO - ========================================
2025-09-18 09:12:44,630 - INFO - Download completed successfully: Steven.Universe.S02E08.1080p.BluRay.x264-TAXES
2025-09-18 09:12:44,630 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E08.1080p.BluRay.x264-TAXES
2025-09-18 09:12:44,630 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:12:44,630 - ERROR - Post-processing failed
2025-09-18 09:12:55,372 - INFO - ============================================================
2025-09-18 09:12:55,372 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:12:55,372 - INFO - Timestamp: 2025-09-18T13:12:55.372450+00:00
2025-09-18 09:12:55,372 - INFO - ============================================================
2025-09-18 09:12:55,372 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:12:55,372 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E09.1080p.BluRay.x264-TAXES
2025-09-18 09:12:55,372 - INFO -   %2 (original_nzb_name): Steven.Universe.S02E09.1080p.BluRay.x264-TAXES.nzb
2025-09-18 09:12:55,372 - INFO -   %3 (clean_job_name): Steven.Universe.S02E09.1080p.BluRay.x264-TAXES
2025-09-18 09:12:55,372 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:12:55,372 - INFO -   %5 (category): tv
2025-09-18 09:12:55,372 - INFO -   %6 (group): alt.binaries.flowed
2025-09-18 09:12:55,372 - INFO -   %7 (post_process_status): 0
2025-09-18 09:12:55,372 - INFO - ========================================
2025-09-18 09:12:55,372 - INFO - Download completed successfully: Steven.Universe.S02E09.1080p.BluRay.x264-TAXES
2025-09-18 09:12:55,372 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E09.1080p.BluRay.x264-TAXES
2025-09-18 09:12:55,373 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:12:55,373 - ERROR - Post-processing failed
2025-09-18 09:13:06,170 - INFO - ============================================================
2025-09-18 09:13:06,170 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:13:06,170 - INFO - Timestamp: 2025-09-18T13:13:06.170545+00:00
2025-09-18 09:13:06,170 - INFO - ============================================================
2025-09-18 09:13:06,170 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:13:06,170 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E10.1080p.BluRay.x264-TAXES
2025-09-18 09:13:06,170 - INFO -   %2 (original_nzb_name): Steven.Universe.S02E10.1080p.BluRay.x264-TAXES.nzb
2025-09-18 09:13:06,170 - INFO -   %3 (clean_job_name): Steven.Universe.S02E10.1080p.BluRay.x264-TAXES
2025-09-18 09:13:06,170 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:13:06,170 - INFO -   %5 (category): tv
2025-09-18 09:13:06,170 - INFO -   %6 (group): alt.binaries.superman
2025-09-18 09:13:06,170 - INFO -   %7 (post_process_status): 0
2025-09-18 09:13:06,170 - INFO - ========================================
2025-09-18 09:13:06,170 - INFO - Download completed successfully: Steven.Universe.S02E10.1080p.BluRay.x264-TAXES
2025-09-18 09:13:06,170 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E10.1080p.BluRay.x264-TAXES
2025-09-18 09:13:06,170 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:13:06,170 - ERROR - Post-processing failed
2025-09-18 09:13:16,846 - INFO - ============================================================
2025-09-18 09:13:16,846 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:13:16,846 - INFO - Timestamp: 2025-09-18T13:13:16.846485+00:00
2025-09-18 09:13:16,846 - INFO - ============================================================
2025-09-18 09:13:16,846 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:13:16,846 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E11.1080p.BluRay.x264-TAXES
2025-09-18 09:13:16,846 - INFO -   %2 (original_nzb_name): Steven.Universe.S02E11.1080p.BluRay.x264-TAXES.nzb
2025-09-18 09:13:16,846 - INFO -   %3 (clean_job_name): Steven.Universe.S02E11.1080p.BluRay.x264-TAXES
2025-09-18 09:13:16,846 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:13:16,846 - INFO -   %5 (category): tv
2025-09-18 09:13:16,846 - INFO -   %6 (group): alt.binaries.superman
2025-09-18 09:13:16,846 - INFO -   %7 (post_process_status): 0
2025-09-18 09:13:16,846 - INFO - ========================================
2025-09-18 09:13:16,846 - INFO - Download completed successfully: Steven.Universe.S02E11.1080p.BluRay.x264-TAXES
2025-09-18 09:13:16,846 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E11.1080p.BluRay.x264-TAXES
2025-09-18 09:13:16,846 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:13:16,846 - ERROR - Post-processing failed
2025-09-18 09:13:27,538 - INFO - ============================================================
2025-09-18 09:13:27,538 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:13:27,538 - INFO - Timestamp: 2025-09-18T13:13:27.538540+00:00
2025-09-18 09:13:27,538 - INFO - ============================================================
2025-09-18 09:13:27,538 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:13:27,538 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E12.1080p.BluRay.x264-TAXES
2025-09-18 09:13:27,538 - INFO -   %2 (original_nzb_name): Steven.Universe.S02E12.1080p.BluRay.x264-TAXES.nzb
2025-09-18 09:13:27,538 - INFO -   %3 (clean_job_name): Steven.Universe.S02E12.1080p.BluRay.x264-TAXES
2025-09-18 09:13:27,538 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:13:27,538 - INFO -   %5 (category): tv
2025-09-18 09:13:27,538 - INFO -   %6 (group): alt.binaries.xylo
2025-09-18 09:13:27,538 - INFO -   %7 (post_process_status): 0
2025-09-18 09:13:27,538 - INFO - ========================================
2025-09-18 09:13:27,538 - INFO - Download completed successfully: Steven.Universe.S02E12.1080p.BluRay.x264-TAXES
2025-09-18 09:13:27,538 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E12.1080p.BluRay.x264-TAXES
2025-09-18 09:13:27,538 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:13:27,538 - ERROR - Post-processing failed
2025-09-18 09:13:40,381 - INFO - ============================================================
2025-09-18 09:13:40,381 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:13:40,381 - INFO - Timestamp: 2025-09-18T13:13:40.381200+00:00
2025-09-18 09:13:40,381 - INFO - ============================================================
2025-09-18 09:13:40,381 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:13:40,381 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E13.1080p.BluRay.x264-TAXES
2025-09-18 09:13:40,381 - INFO -   %2 (original_nzb_name): Steven.Universe.S02E13.1080p.BluRay.x264-TAXES.nzb
2025-09-18 09:13:40,381 - INFO -   %3 (clean_job_name): Steven.Universe.S02E13.1080p.BluRay.x264-TAXES
2025-09-18 09:13:40,381 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:13:40,381 - INFO -   %5 (category): tv
2025-09-18 09:13:40,381 - INFO -   %6 (group): alt.binaries.xylo
2025-09-18 09:13:40,381 - INFO -   %7 (post_process_status): 0
2025-09-18 09:13:40,381 - INFO - ========================================
2025-09-18 09:13:40,381 - INFO - Download completed successfully: Steven.Universe.S02E13.1080p.BluRay.x264-TAXES
2025-09-18 09:13:40,381 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E13.1080p.BluRay.x264-TAXES
2025-09-18 09:13:40,381 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:13:40,381 - ERROR - Post-processing failed
2025-09-18 09:13:49,556 - INFO - ============================================================
2025-09-18 09:13:49,556 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:13:49,556 - INFO - Timestamp: 2025-09-18T13:13:49.556495+00:00
2025-09-18 09:13:49,556 - INFO - ============================================================
2025-09-18 09:13:49,556 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:13:49,556 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E14.1080p.BluRay.x264-TAXES
2025-09-18 09:13:49,556 - INFO -   %2 (original_nzb_name): Steven.Universe.S02E14.1080p.BluRay.x264-TAXES.nzb
2025-09-18 09:13:49,556 - INFO -   %3 (clean_job_name): Steven.Universe.S02E14.1080p.BluRay.x264-TAXES
2025-09-18 09:13:49,556 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:13:49,556 - INFO -   %5 (category): tv
2025-09-18 09:13:49,556 - INFO -   %6 (group): alt.binaries.goonies
2025-09-18 09:13:49,556 - INFO -   %7 (post_process_status): 0
2025-09-18 09:13:49,556 - INFO - ========================================
2025-09-18 09:13:49,556 - INFO - Download completed successfully: Steven.Universe.S02E14.1080p.BluRay.x264-TAXES
2025-09-18 09:13:49,556 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E14.1080p.BluRay.x264-TAXES
2025-09-18 09:13:49,556 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:13:49,556 - ERROR - Post-processing failed
2025-09-18 09:14:02,402 - INFO - ============================================================
2025-09-18 09:14:02,402 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:14:02,402 - INFO - Timestamp: 2025-09-18T13:14:02.402692+00:00
2025-09-18 09:14:02,402 - INFO - ============================================================
2025-09-18 09:14:02,402 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:14:02,402 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E15.1080p.BluRay.x264-TAXES
2025-09-18 09:14:02,402 - INFO -   %2 (original_nzb_name): Steven.Universe.S02E15.1080p.BluRay.x264-TAXES.nzb
2025-09-18 09:14:02,402 - INFO -   %3 (clean_job_name): Steven.Universe.S02E15.1080p.BluRay.x264-TAXES
2025-09-18 09:14:02,402 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:14:02,402 - INFO -   %5 (category): tv
2025-09-18 09:14:02,402 - INFO -   %6 (group): alt.binaries.goonies
2025-09-18 09:14:02,402 - INFO -   %7 (post_process_status): 0
2025-09-18 09:14:02,402 - INFO - ========================================
2025-09-18 09:14:02,402 - INFO - Download completed successfully: Steven.Universe.S02E15.1080p.BluRay.x264-TAXES
2025-09-18 09:14:02,403 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E15.1080p.BluRay.x264-TAXES
2025-09-18 09:14:02,403 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:14:02,403 - ERROR - Post-processing failed
2025-09-18 09:14:11,216 - INFO - ============================================================
2025-09-18 09:14:11,216 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:14:11,217 - INFO - Timestamp: 2025-09-18T13:14:11.217028+00:00
2025-09-18 09:14:11,217 - INFO - ============================================================
2025-09-18 09:14:11,217 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:14:11,217 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E16.1080p.BluRay.x264-TAXES
2025-09-18 09:14:11,217 - INFO -   %2 (original_nzb_name): Steven.Universe.S02E16.1080p.BluRay.x264-TAXES.nzb
2025-09-18 09:14:11,217 - INFO -   %3 (clean_job_name): Steven.Universe.S02E16.1080p.BluRay.x264-TAXES
2025-09-18 09:14:11,217 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:14:11,217 - INFO -   %5 (category): tv
2025-09-18 09:14:11,217 - INFO -   %6 (group): alt.binaries.goat
2025-09-18 09:14:11,217 - INFO -   %7 (post_process_status): 0
2025-09-18 09:14:11,217 - INFO - ========================================
2025-09-18 09:14:11,217 - INFO - Download completed successfully: Steven.Universe.S02E16.1080p.BluRay.x264-TAXES
2025-09-18 09:14:11,217 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E16.1080p.BluRay.x264-TAXES
2025-09-18 09:14:11,217 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:14:11,217 - ERROR - Post-processing failed
2025-09-18 09:14:21,741 - INFO - ============================================================
2025-09-18 09:14:21,741 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:14:21,741 - INFO - Timestamp: 2025-09-18T13:14:21.741508+00:00
2025-09-18 09:14:21,741 - INFO - ============================================================
2025-09-18 09:14:21,741 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:14:21,741 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E17.1080p.BluRay.x264-TAXES
2025-09-18 09:14:21,741 - INFO -   %2 (original_nzb_name): Steven.Universe.S02E17.1080p.BluRay.x264-TAXES.nzb
2025-09-18 09:14:21,741 - INFO -   %3 (clean_job_name): Steven.Universe.S02E17.1080p.BluRay.x264-TAXES
2025-09-18 09:14:21,741 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:14:21,741 - INFO -   %5 (category): tv
2025-09-18 09:14:21,741 - INFO -   %6 (group): alt.binaries.goat
2025-09-18 09:14:21,741 - INFO -   %7 (post_process_status): 0
2025-09-18 09:14:21,741 - INFO - ========================================
2025-09-18 09:14:21,741 - INFO - Download completed successfully: Steven.Universe.S02E17.1080p.BluRay.x264-TAXES
2025-09-18 09:14:21,741 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E17.1080p.BluRay.x264-TAXES
2025-09-18 09:14:21,741 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:14:21,741 - ERROR - Post-processing failed
2025-09-18 09:14:32,256 - INFO - ============================================================
2025-09-18 09:14:32,256 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:14:32,256 - INFO - Timestamp: 2025-09-18T13:14:32.256388+00:00
2025-09-18 09:14:32,256 - INFO - ============================================================
2025-09-18 09:14:32,256 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:14:32,256 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E18.1080p.BluRay.x264-TAXES
2025-09-18 09:14:32,256 - INFO -   %2 (original_nzb_name): Steven.Universe.S02E18.1080p.BluRay.x264-TAXES.nzb
2025-09-18 09:14:32,256 - INFO -   %3 (clean_job_name): Steven.Universe.S02E18.1080p.BluRay.x264-TAXES
2025-09-18 09:14:32,256 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:14:32,256 - INFO -   %5 (category): tv
2025-09-18 09:14:32,256 - INFO -   %6 (group): alt.binaries.flowed
2025-09-18 09:14:32,256 - INFO -   %7 (post_process_status): 0
2025-09-18 09:14:32,256 - INFO - ========================================
2025-09-18 09:14:32,256 - INFO - Download completed successfully: Steven.Universe.S02E18.1080p.BluRay.x264-TAXES
2025-09-18 09:14:32,256 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E18.1080p.BluRay.x264-TAXES
2025-09-18 09:14:32,256 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:14:32,256 - ERROR - Post-processing failed
2025-09-18 09:14:36,888 - INFO - ============================================================
2025-09-18 09:14:36,889 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:14:36,889 - INFO - Timestamp: 2025-09-18T13:14:36.889081+00:00
2025-09-18 09:14:36,889 - INFO - ============================================================
2025-09-18 09:14:36,889 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:14:36,889 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S01E48.720p.BluRay.x264-TAXES
2025-09-18 09:14:36,889 - INFO -   %2 (original_nzb_name): Steven.Universe.S01E48.720p.BluRay.x264-TAXES.nzb
2025-09-18 09:14:36,889 - INFO -   %3 (clean_job_name): Steven.Universe.S01E48.720p.BluRay.x264-TAXES
2025-09-18 09:14:36,889 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:14:36,889 - INFO -   %5 (category): tv
2025-09-18 09:14:36,889 - INFO -   %6 (group): alt.binaries.goat
2025-09-18 09:14:36,889 - INFO -   %7 (post_process_status): 0
2025-09-18 09:14:36,889 - INFO - ========================================
2025-09-18 09:14:36,889 - INFO - Download completed successfully: Steven.Universe.S01E48.720p.BluRay.x264-TAXES
2025-09-18 09:14:36,889 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S01E48.720p.BluRay.x264-TAXES
2025-09-18 09:14:36,889 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:14:36,889 - ERROR - Post-processing failed
2025-09-18 09:14:47,172 - INFO - ============================================================
2025-09-18 09:14:47,172 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:14:47,172 - INFO - Timestamp: 2025-09-18T13:14:47.172426+00:00
2025-09-18 09:14:47,172 - INFO - ============================================================
2025-09-18 09:14:47,172 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:14:47,172 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E19.1080p.BluRay.x264-TAXES
2025-09-18 09:14:47,172 - INFO -   %2 (original_nzb_name): Steven.Universe.S02E19.1080p.BluRay.x264-TAXES.nzb
2025-09-18 09:14:47,172 - INFO -   %3 (clean_job_name): Steven.Universe.S02E19.1080p.BluRay.x264-TAXES
2025-09-18 09:14:47,172 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:14:47,172 - INFO -   %5 (category): tv
2025-09-18 09:14:47,172 - INFO -   %6 (group): alt.binaries.flowed
2025-09-18 09:14:47,172 - INFO -   %7 (post_process_status): 0
2025-09-18 09:14:47,172 - INFO - ========================================
2025-09-18 09:14:47,172 - INFO - Download completed successfully: Steven.Universe.S02E19.1080p.BluRay.x264-TAXES
2025-09-18 09:14:47,172 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E19.1080p.BluRay.x264-TAXES
2025-09-18 09:14:47,172 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:14:47,172 - ERROR - Post-processing failed
2025-09-18 09:14:51,816 - INFO - ============================================================
2025-09-18 09:14:51,816 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:14:51,816 - INFO - Timestamp: 2025-09-18T13:14:51.816702+00:00
2025-09-18 09:14:51,816 - INFO - ============================================================
2025-09-18 09:14:51,816 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:14:51,816 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S01E47.720p.BluRay.x264-TAXES
2025-09-18 09:14:51,816 - INFO -   %2 (original_nzb_name): Steven.Universe.S01E47.720p.BluRay.x264-TAXES.nzb
2025-09-18 09:14:51,816 - INFO -   %3 (clean_job_name): Steven.Universe.S01E47.720p.BluRay.x264-TAXES
2025-09-18 09:14:51,816 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:14:51,816 - INFO -   %5 (category): tv
2025-09-18 09:14:51,816 - INFO -   %6 (group): alt.binaries.flowed
2025-09-18 09:14:51,816 - INFO -   %7 (post_process_status): 0
2025-09-18 09:14:51,816 - INFO - ========================================
2025-09-18 09:14:51,816 - INFO - Download completed successfully: Steven.Universe.S01E47.720p.BluRay.x264-TAXES
2025-09-18 09:14:51,817 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S01E47.720p.BluRay.x264-TAXES
2025-09-18 09:14:51,817 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:14:51,817 - ERROR - Post-processing failed
2025-09-18 09:15:02,269 - INFO - ============================================================
2025-09-18 09:15:02,269 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:15:02,269 - INFO - Timestamp: 2025-09-18T13:15:02.269944+00:00
2025-09-18 09:15:02,269 - INFO - ============================================================
2025-09-18 09:15:02,270 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:15:02,270 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E20.1080p.BluRay.x264-TAXES
2025-09-18 09:15:02,270 - INFO -   %2 (original_nzb_name): Steven.Universe.S02E20.1080p.BluRay.x264-TAXES.nzb
2025-09-18 09:15:02,270 - INFO -   %3 (clean_job_name): Steven.Universe.S02E20.1080p.BluRay.x264-TAXES
2025-09-18 09:15:02,270 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:15:02,270 - INFO -   %5 (category): tv
2025-09-18 09:15:02,270 - INFO -   %6 (group): alt.binaries.superman
2025-09-18 09:15:02,270 - INFO -   %7 (post_process_status): 0
2025-09-18 09:15:02,270 - INFO - ========================================
2025-09-18 09:15:02,270 - INFO - Download completed successfully: Steven.Universe.S02E20.1080p.BluRay.x264-TAXES
2025-09-18 09:15:02,270 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E20.1080p.BluRay.x264-TAXES
2025-09-18 09:15:02,270 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:15:02,270 - ERROR - Post-processing failed
2025-09-18 09:15:15,096 - INFO - ============================================================
2025-09-18 09:15:15,096 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:15:15,096 - INFO - Timestamp: 2025-09-18T13:15:15.096572+00:00
2025-09-18 09:15:15,096 - INFO - ============================================================
2025-09-18 09:15:15,096 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:15:15,096 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E21.1080p.BluRay.x264-TAXES
2025-09-18 09:15:15,096 - INFO -   %2 (original_nzb_name): Steven.Universe.S02E21.1080p.BluRay.x264-TAXES.nzb
2025-09-18 09:15:15,096 - INFO -   %3 (clean_job_name): Steven.Universe.S02E21.1080p.BluRay.x264-TAXES
2025-09-18 09:15:15,096 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:15:15,096 - INFO -   %5 (category): tv
2025-09-18 09:15:15,096 - INFO -   %6 (group): alt.binaries.superman
2025-09-18 09:15:15,096 - INFO -   %7 (post_process_status): 0
2025-09-18 09:15:15,096 - INFO - ========================================
2025-09-18 09:15:15,096 - INFO - Download completed successfully: Steven.Universe.S02E21.1080p.BluRay.x264-TAXES
2025-09-18 09:15:15,096 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E21.1080p.BluRay.x264-TAXES
2025-09-18 09:15:15,096 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:15:15,096 - ERROR - Post-processing failed
2025-09-18 09:15:23,898 - INFO - ============================================================
2025-09-18 09:15:23,898 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:15:23,898 - INFO - Timestamp: 2025-09-18T13:15:23.898697+00:00
2025-09-18 09:15:23,898 - INFO - ============================================================
2025-09-18 09:15:23,898 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:15:23,898 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E22.1080p.BluRay.x264-TAXES
2025-09-18 09:15:23,898 - INFO -   %2 (original_nzb_name): Steven.Universe.S02E22.1080p.BluRay.x264-TAXES.nzb
2025-09-18 09:15:23,898 - INFO -   %3 (clean_job_name): Steven.Universe.S02E22.1080p.BluRay.x264-TAXES
2025-09-18 09:15:23,898 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:15:23,898 - INFO -   %5 (category): tv
2025-09-18 09:15:23,898 - INFO -   %6 (group): alt.binaries.xylo
2025-09-18 09:15:23,898 - INFO -   %7 (post_process_status): 0
2025-09-18 09:15:23,898 - INFO - ========================================
2025-09-18 09:15:23,898 - INFO - Download completed successfully: Steven.Universe.S02E22.1080p.BluRay.x264-TAXES
2025-09-18 09:15:23,898 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E22.1080p.BluRay.x264-TAXES
2025-09-18 09:15:23,899 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:15:23,899 - ERROR - Post-processing failed
2025-09-18 09:15:36,743 - INFO - ============================================================
2025-09-18 09:15:36,743 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:15:36,743 - INFO - Timestamp: 2025-09-18T13:15:36.743484+00:00
2025-09-18 09:15:36,743 - INFO - ============================================================
2025-09-18 09:15:36,743 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:15:36,743 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E23.1080p.BluRay.x264-TAXES
2025-09-18 09:15:36,743 - INFO -   %2 (original_nzb_name): Steven.Universe.S02E23.1080p.BluRay.x264-TAXES.nzb
2025-09-18 09:15:36,743 - INFO -   %3 (clean_job_name): Steven.Universe.S02E23.1080p.BluRay.x264-TAXES
2025-09-18 09:15:36,743 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:15:36,743 - INFO -   %5 (category): tv
2025-09-18 09:15:36,743 - INFO -   %6 (group): alt.binaries.xylo
2025-09-18 09:15:36,743 - INFO -   %7 (post_process_status): 0
2025-09-18 09:15:36,743 - INFO - ========================================
2025-09-18 09:15:36,743 - INFO - Download completed successfully: Steven.Universe.S02E23.1080p.BluRay.x264-TAXES
2025-09-18 09:15:36,743 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E23.1080p.BluRay.x264-TAXES
2025-09-18 09:15:36,743 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:15:36,743 - ERROR - Post-processing failed
2025-09-18 09:15:45,672 - INFO - ============================================================
2025-09-18 09:15:45,672 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:15:45,672 - INFO - Timestamp: 2025-09-18T13:15:45.672607+00:00
2025-09-18 09:15:45,672 - INFO - ============================================================
2025-09-18 09:15:45,672 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:15:45,672 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E24.1080p.BluRay.x264-TAXES
2025-09-18 09:15:45,672 - INFO -   %2 (original_nzb_name): Steven.Universe.S02E24.1080p.BluRay.x264-TAXES.nzb
2025-09-18 09:15:45,672 - INFO -   %3 (clean_job_name): Steven.Universe.S02E24.1080p.BluRay.x264-TAXES
2025-09-18 09:15:45,672 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:15:45,672 - INFO -   %5 (category): tv
2025-09-18 09:15:45,672 - INFO -   %6 (group): alt.binaries.goonies
2025-09-18 09:15:45,672 - INFO -   %7 (post_process_status): 0
2025-09-18 09:15:45,672 - INFO - ========================================
2025-09-18 09:15:45,672 - INFO - Download completed successfully: Steven.Universe.S02E24.1080p.BluRay.x264-TAXES
2025-09-18 09:15:45,672 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E24.1080p.BluRay.x264-TAXES
2025-09-18 09:15:45,672 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:15:45,672 - ERROR - Post-processing failed
2025-09-18 09:15:58,513 - INFO - ============================================================
2025-09-18 09:15:58,513 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:15:58,513 - INFO - Timestamp: 2025-09-18T13:15:58.513833+00:00
2025-09-18 09:15:58,513 - INFO - ============================================================
2025-09-18 09:15:58,513 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:15:58,513 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E25.1080p.BluRay.x264-TAXES
2025-09-18 09:15:58,513 - INFO -   %2 (original_nzb_name): Steven.Universe.S02E25.1080p.BluRay.x264-TAXES.nzb
2025-09-18 09:15:58,513 - INFO -   %3 (clean_job_name): Steven.Universe.S02E25.1080p.BluRay.x264-TAXES
2025-09-18 09:15:58,514 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:15:58,514 - INFO -   %5 (category): tv
2025-09-18 09:15:58,514 - INFO -   %6 (group): alt.binaries.goonies
2025-09-18 09:15:58,514 - INFO -   %7 (post_process_status): 0
2025-09-18 09:15:58,514 - INFO - ========================================
2025-09-18 09:15:58,514 - INFO - Download completed successfully: Steven.Universe.S02E25.1080p.BluRay.x264-TAXES
2025-09-18 09:15:58,514 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E25.1080p.BluRay.x264-TAXES
2025-09-18 09:15:58,514 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:15:58,514 - ERROR - Post-processing failed
2025-09-18 09:19:56,846 - INFO - ============================================================
2025-09-18 09:19:56,846 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:19:56,846 - INFO - Timestamp: 2025-09-18T13:19:56.846988+00:00
2025-09-18 09:19:56,847 - INFO - ============================================================
2025-09-18 09:19:56,847 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:19:56,847 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E26.1080p.BluRay.x264-TAXES
2025-09-18 09:19:56,847 - INFO -   %2 (original_nzb_name): Steven.Universe.S02E26.1080p.BluRay.x264-TAXES.nzb
2025-09-18 09:19:56,847 - INFO -   %3 (clean_job_name): Steven.Universe.S02E26.1080p.BluRay.x264-TAXES
2025-09-18 09:19:56,847 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:19:56,847 - INFO -   %5 (category): tv
2025-09-18 09:19:56,847 - INFO -   %6 (group): alt.binaries.multimedia.alias
2025-09-18 09:19:56,847 - INFO -   %7 (post_process_status): 0
2025-09-18 09:19:56,847 - INFO - ========================================
2025-09-18 09:19:56,847 - INFO - Download completed successfully: Steven.Universe.S02E26.1080p.BluRay.x264-TAXES
2025-09-18 09:19:56,847 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Steven.Universe.S02E26.1080p.BluRay.x264-TAXES
2025-09-18 09:19:56,847 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:19:56,847 - ERROR - Post-processing failed
2025-09-18 09:20:11,263 - INFO - ============================================================
2025-09-18 09:20:11,263 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:20:11,263 - INFO - Timestamp: 2025-09-18T13:20:11.263162+00:00
2025-09-18 09:20:11,263 - INFO - ============================================================
2025-09-18 09:20:11,263 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:20:11,263 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:20:11,263 - INFO -   %2 (original_nzb_name): Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.nzb
2025-09-18 09:20:11,263 - INFO -   %3 (clean_job_name): Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:20:11,263 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:20:11,263 - INFO -   %5 (category): tv
2025-09-18 09:20:11,263 - INFO -   %6 (group): alt.binaries.multimedia.aviation
2025-09-18 09:20:11,263 - INFO -   %7 (post_process_status): 0
2025-09-18 09:20:11,263 - INFO - ========================================
2025-09-18 09:20:11,263 - INFO - Download completed successfully: Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:20:11,263 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:20:11,263 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:20:11,263 - ERROR - Post-processing failed
2025-09-18 09:20:27,273 - INFO - ============================================================
2025-09-18 09:20:27,273 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:20:27,273 - INFO - Timestamp: 2025-09-18T13:20:27.273460+00:00
2025-09-18 09:20:27,273 - INFO - ============================================================
2025-09-18 09:20:27,273 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:20:27,273 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:20:27,273 - INFO -   %2 (original_nzb_name): Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.nzb
2025-09-18 09:20:27,273 - INFO -   %3 (clean_job_name): Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:20:27,273 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:20:27,273 - INFO -   %5 (category): tv
2025-09-18 09:20:27,273 - INFO -   %6 (group): alt.binaries.multimedia.aviation
2025-09-18 09:20:27,273 - INFO -   %7 (post_process_status): 0
2025-09-18 09:20:27,273 - INFO - ========================================
2025-09-18 09:20:27,273 - INFO - Download completed successfully: Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:20:27,273 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:20:27,273 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:20:27,273 - ERROR - Post-processing failed
2025-09-18 09:20:43,056 - INFO - ============================================================
2025-09-18 09:20:43,056 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:20:43,056 - INFO - Timestamp: 2025-09-18T13:20:43.056601+00:00
2025-09-18 09:20:43,056 - INFO - ============================================================
2025-09-18 09:20:43,056 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:20:43,056 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:20:43,056 - INFO -   %2 (original_nzb_name): Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.nzb
2025-09-18 09:20:43,056 - INFO -   %3 (clean_job_name): Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:20:43,056 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:20:43,056 - INFO -   %5 (category): tv
2025-09-18 09:20:43,056 - INFO -   %6 (group): alt.binaries.bungabunga
2025-09-18 09:20:43,056 - INFO -   %7 (post_process_status): 0
2025-09-18 09:20:43,057 - INFO - ========================================
2025-09-18 09:20:43,057 - INFO - Download completed successfully: Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:20:43,057 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:20:43,057 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:20:43,057 - ERROR - Post-processing failed
2025-09-18 09:20:58,373 - INFO - ============================================================
2025-09-18 09:20:58,374 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:20:58,374 - INFO - Timestamp: 2025-09-18T13:20:58.374077+00:00
2025-09-18 09:20:58,374 - INFO - ============================================================
2025-09-18 09:20:58,374 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:20:58,374 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:20:58,374 - INFO -   %2 (original_nzb_name): Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.nzb
2025-09-18 09:20:58,374 - INFO -   %3 (clean_job_name): Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:20:58,374 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:20:58,374 - INFO -   %5 (category): tv
2025-09-18 09:20:58,374 - INFO -   %6 (group): alt.binaries.coolkidweb
2025-09-18 09:20:58,374 - INFO -   %7 (post_process_status): 0
2025-09-18 09:20:58,374 - INFO - ========================================
2025-09-18 09:20:58,374 - INFO - Download completed successfully: Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:20:58,374 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:20:58,374 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:20:58,374 - ERROR - Post-processing failed
2025-09-18 09:21:16,537 - INFO - ============================================================
2025-09-18 09:21:16,537 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:21:16,538 - INFO - Timestamp: 2025-09-18T13:21:16.537997+00:00
2025-09-18 09:21:16,538 - INFO - ============================================================
2025-09-18 09:21:16,538 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:21:16,538 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:21:16,538 - INFO -   %2 (original_nzb_name): Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.nzb
2025-09-18 09:21:16,538 - INFO -   %3 (clean_job_name): Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:21:16,538 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:21:16,538 - INFO -   %5 (category): tv
2025-09-18 09:21:16,538 - INFO -   %6 (group): alt.binaries.warcraft
2025-09-18 09:21:16,538 - INFO -   %7 (post_process_status): 0
2025-09-18 09:21:16,538 - INFO - ========================================
2025-09-18 09:21:16,538 - INFO - Download completed successfully: Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:21:16,538 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:21:16,538 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:21:16,538 - ERROR - Post-processing failed
2025-09-18 09:21:34,709 - INFO - ============================================================
2025-09-18 09:21:34,709 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:21:34,709 - INFO - Timestamp: 2025-09-18T13:21:34.709480+00:00
2025-09-18 09:21:34,709 - INFO - ============================================================
2025-09-18 09:21:34,709 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:21:34,709 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:21:34,709 - INFO -   %2 (original_nzb_name): Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.nzb
2025-09-18 09:21:34,709 - INFO -   %3 (clean_job_name): Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:21:34,709 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:21:34,709 - INFO -   %5 (category): tv
2025-09-18 09:21:34,709 - INFO -   %6 (group): alt.binaries.bungabunga
2025-09-18 09:21:34,709 - INFO -   %7 (post_process_status): 0
2025-09-18 09:21:34,709 - INFO - ========================================
2025-09-18 09:21:34,709 - INFO - Download completed successfully: Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:21:34,709 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:21:34,709 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:21:34,709 - ERROR - Post-processing failed
2025-09-18 09:21:50,542 - INFO - ============================================================
2025-09-18 09:21:50,542 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:21:50,543 - INFO - Timestamp: 2025-09-18T13:21:50.543080+00:00
2025-09-18 09:21:50,543 - INFO - ============================================================
2025-09-18 09:21:50,543 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:21:50,543 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:21:50,543 - INFO -   %2 (original_nzb_name): Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.nzb
2025-09-18 09:21:50,543 - INFO -   %3 (clean_job_name): Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:21:50,543 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:21:50,543 - INFO -   %5 (category): tv
2025-09-18 09:21:50,543 - INFO -   %6 (group): alt.binaries.coolkidweb
2025-09-18 09:21:50,543 - INFO -   %7 (post_process_status): 0
2025-09-18 09:21:50,543 - INFO - ========================================
2025-09-18 09:21:50,543 - INFO - Download completed successfully: Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:21:50,543 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:21:50,543 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:21:50,543 - ERROR - Post-processing failed
2025-09-18 09:22:08,694 - INFO - ============================================================
2025-09-18 09:22:08,694 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:22:08,694 - INFO - Timestamp: 2025-09-18T13:22:08.694442+00:00
2025-09-18 09:22:08,694 - INFO - ============================================================
2025-09-18 09:22:08,694 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:22:08,694 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:22:08,694 - INFO -   %2 (original_nzb_name): Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.nzb
2025-09-18 09:22:08,694 - INFO -   %3 (clean_job_name): Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:22:08,694 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:22:08,694 - INFO -   %5 (category): tv
2025-09-18 09:22:08,694 - INFO -   %6 (group): alt.binaries.warcraft
2025-09-18 09:22:08,694 - INFO -   %7 (post_process_status): 0
2025-09-18 09:22:08,694 - INFO - ========================================
2025-09-18 09:22:08,694 - INFO - Download completed successfully: Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:22:08,694 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:22:08,694 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:22:08,694 - ERROR - Post-processing failed
2025-09-18 09:22:27,077 - INFO - ============================================================
2025-09-18 09:22:27,077 - INFO - SABnzbd Post-Process Bridge Script Started
2025-09-18 09:22:27,077 - INFO - Timestamp: 2025-09-18T13:22:27.077612+00:00
2025-09-18 09:22:27,077 - INFO - ============================================================
2025-09-18 09:22:27,077 - INFO - === SABnzbd Post-Process Parameters ===
2025-09-18 09:22:27,077 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:22:27,077 - INFO -   %2 (original_nzb_name): Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.nzb
2025-09-18 09:22:27,077 - INFO -   %3 (clean_job_name): Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:22:27,077 - INFO -   %4 (indexer_report_number): 
2025-09-18 09:22:27,077 - INFO -   %5 (category): tv
2025-09-18 09:22:27,077 - INFO -   %6 (group): alt.binaries.multimedia.aviation
2025-09-18 09:22:27,077 - INFO -   %7 (post_process_status): 0
2025-09-18 09:22:27,077 - INFO - ========================================
2025-09-18 09:22:27,077 - INFO - Download completed successfully: Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:22:27,077 - INFO - Final folder: C:\Users\<USER>\Videos\PlexAutomator\workspace\1_downloading\complete_raw\Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
2025-09-18 09:22:27,077 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexAutomator\_internal\_internal\src\main_pipeline_orchestrator.py
2025-09-18 09:22:27,077 - ERROR - Post-processing failed
