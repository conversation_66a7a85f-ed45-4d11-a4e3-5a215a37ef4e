{"episodes": [{"guid": "https://nzbfinder.ws/details/d1a6dff5-57c6-4bff-970a-f94fcb822a64", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E01.Full.Disclosure.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61539, "decision": "ACCEPT", "risk_score": 0.021783333956386187, "probe_missing_ratio": 0.0, "size": *********, "cached": false}, {"guid": "https://nzbfinder.ws/details/6e1430bc-ee83-442b-8eb5-19860aeb467c", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E01.720p.BluRay.x264-TAXES", "episode_id": 61539, "decision": "ACCEPT", "risk_score": 0.13235841806184617, "probe_missing_ratio": 0.004545454545454545, "size": *********, "cached": false}, {"guid": "https://nzbfinder.ws/details/111a8f1b-92ad-43b0-88c2-dbf10c9c849e", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E01.1080p.BluRay.x264-TAXES", "episode_id": 61539, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": false}, {"guid": "https://nzbfinder.ws/details/d48ed3fd-6c33-4b48-97f9-bdeb49f7f922", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E01.1080p.BluRay.x264-TAXES", "episode_id": 61539, "decision": "ACCEPT", "risk_score": 0.1311603843102389, "probe_missing_ratio": 0.0, "size": *********, "cached": false}, {"guid": "https://nzbfinder.ws/details/cc9332c1-f2d8-4e6b-9974-7ebf8df9d64f", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S01E46.720p.BluRay.x264-TAXES", "episode_id": 61540, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/3a80879a-4090-4f98-9f80-4e33afae9e6f", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S01E46.1080p.BluRay.x264-TAXES", "episode_id": 61540, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/9bc37f09-d82a-47c6-b559-05d7d4dfb07a", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E02.Open.Book.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61541, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/441b61a9-d351-4515-9d27-e578704a56a4", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E02.720p.BluRay.x264-TAXES", "episode_id": 61541, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/661ff7b4-8f89-44aa-b3ad-43f68fcedd09", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E02.1080p.BluRay.x264-TAXES", "episode_id": 61541, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/dc3d9885-c884-4c12-b193-75f8ec4e02ea", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E02.1080p.BluRay.x264-TAXES", "episode_id": 61541, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/1af9eae1-a4a3-4987-a59b-c4e459079c38", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E03.Joy.Ride.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61542, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/e18de746-40fa-474d-9bd7-f0ac5cd5493f", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E03.720p.BluRay.x264-TAXES", "episode_id": 61542, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/2173760d-be3b-47f6-94e1-858b9ceeed88", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E03.1080p.BluRay.x264-TAXES", "episode_id": 61542, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/9a43bf0d-543b-42b1-925f-54a3399efdbe", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E03.1080p.BluRay.x264-TAXES", "episode_id": 61542, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/288ff011-d5d6-4bd6-b28e-a91801c5afc2", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S01E48.720p.BluRay.x264-TAXES", "episode_id": 61543, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/d161e5f9-f084-4e6b-9951-f4d5bd8952c2", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S01E48.1080p.BluRay.x264-TAXES", "episode_id": 61543, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/013fc6bb-fcea-4648-8c17-7c8a1ed78547", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S01E47.720p.BluRay.x264-TAXES", "episode_id": 61544, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/5f693bce-75f7-477b-8336-8a57a4d3e047", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S01E47.1080p.BluRay.x264-TAXES", "episode_id": 61544, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/8534fc60-7d47-4ff9-8817-a44f19c70da6", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E04.<PERSON><PERSON>.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61545, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/4e06a213-4366-43df-b946-614e21bc2829", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E04.720p.BluRay.x264-TAXES", "episode_id": 61545, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/7bd95553-fef7-4d5e-8ee4-2e0baa48dd7c", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E04.1080p.BluRay.x264-TAXES", "episode_id": 61545, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/3969f110-4228-4e62-8151-0e5a3247ae68", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E04.1080p.BluRay.x264-TAXES", "episode_id": 61545, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/29687f16-ea9d-4db4-9cb1-3801b65040c5", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E05.Story.for.Steven.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61546, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/352128e7-780f-4b0c-9f3a-8271ee14961a", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E05.720p.BluRay.x264-TAXES", "episode_id": 61546, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/67a10a70-0510-44f7-b7ea-3e7d1f118397", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E05.1080p.BluRay.x264-TAXES", "episode_id": 61546, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/70f149b1-98b7-478b-849c-a97d76762fb0", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E05.1080p.BluRay.x264-TAXES", "episode_id": 61546, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/3ea576ec-0e9b-418c-aa15-f252152f8817", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E06.Shirt.Club.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61547, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/ad4cf216-af06-4cd3-a586-c845e08dafb2", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E06.720p.BluRay.x264-TAXES", "episode_id": 61547, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/efa9ef74-7784-47aa-8820-aed8481cc37f", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E06.1080p.BluRay.x264-TAXES", "episode_id": 61547, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/c13f086d-19e9-47f8-adf3-de85d9418a10", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E06.1080p.BluRay.x264-TAXES", "episode_id": 61547, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/04c16d9f-72d3-4dff-849b-09d891d084c1", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E07.Love.Letters.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61548, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/32d47562-6cb4-428b-9c86-1a9848619d86", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E07.720p.BluRay.x264-TAXES", "episode_id": 61548, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/c413e06e-0043-4866-9c43-6ea326f77b88", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E07.1080p.BluRay.x264-TAXES", "episode_id": 61548, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/09c5ccb8-7218-4c9b-bf4e-d7a4869c2806", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E07.1080p.BluRay.x264-TAXES", "episode_id": 61548, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/517f7cd6-83d7-478f-8055-6e38c11ed21b", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E08.Reformed.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61549, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/604e49a1-fb75-46df-9b1c-ed86e98ff498", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E08.720p.BluRay.x264-TAXES", "episode_id": 61549, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/324727a2-cfbe-48a0-a20b-045739c3b803", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E08.1080p.BluRay.x264-TAXES", "episode_id": 61549, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/79e1b31e-b685-4c9a-8b39-cf2d3fafbecc", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E08.1080p.BluRay.x264-TAXES", "episode_id": 61549, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/3c7da46e-994a-4b3d-b15f-d1c955f4e3e6", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E09.Sworn.to.the.Sword.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61550, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/10e5cbdc-721d-462d-a8a0-03bfbdd4236a", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E09.720p.BluRay.x264-TAXES", "episode_id": 61550, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/b625a2be-81b1-4dec-bbff-a38512263b66", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E09.1080p.BluRay.x264-TAXES", "episode_id": 61550, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/7bce1352-49e8-459d-8653-fbe33b3ebe0c", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E09.1080p.BluRay.x264-TAXES", "episode_id": 61550, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/aa57892c-e106-4f99-9436-c47821883f75", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E10.Rising.Tides.Crashing.Skies.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61551, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/42b4ea22-3176-4156-9b36-81ee5440192e", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E10.720p.BluRay.x264-TAXES", "episode_id": 61551, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/59785e32-e859-4f4a-8c3c-e4655d93481e", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E10.1080p.BluRay.x264-TAXES", "episode_id": 61551, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/969cadf5-f9a3-49b7-8e0d-bbcd19a390e9", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E10.1080p.BluRay.x264-TAXES", "episode_id": 61551, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/9138c3a4-b8e1-49a0-94f7-ae045ec57f17", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E11.Keeping.it.Together.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61552, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/98901b80-2606-47c3-9b19-588401aa3bdc", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E11.720p.BluRay.x264-TAXES", "episode_id": 61552, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/dcc79a28-8e34-4919-abc0-b6f8c3167952", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E11.1080p.BluRay.x264-TAXES", "episode_id": 61552, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/9fc8f7e8-2bae-41f0-8964-0e3c342c8ce9", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E11.1080p.BluRay.x264-TAXES", "episode_id": 61552, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/578a76b8-631d-4a6d-8e4c-bbda059215cd", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E12.We.Need.to.Talk.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61553, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/2e1d075f-6c24-44ee-aba0-aa4f9303306a", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E12.720p.BluRay.x264-TAXES", "episode_id": 61553, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/015dd4de-13b4-4988-b4c0-0964dddc4904", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E12.1080p.BluRay.x264-TAXES", "episode_id": 61553, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/943708eb-2aad-46bd-b5a4-0de07eabf051", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E12.1080p.BluRay.x264-TAXES", "episode_id": 61553, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/07dcf58a-d93b-4cbd-b7ff-71546d00a5e1", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E13.Chille.Tid.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61554, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/c34479d7-1d15-4701-988b-2951dce507fc", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E13.720p.BluRay.x264-TAXES", "episode_id": 61554, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/1034dc30-a2a1-4539-a1ca-c91fc7ce6c34", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E13.1080p.BluRay.x264-TAXES", "episode_id": 61554, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/3c4697b1-e729-493e-8ada-7f6b1ffcde10", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E13.1080p.BluRay.x264-TAXES", "episode_id": 61554, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/1c98aaf2-3562-44ba-a7f6-fe210dd11f9e", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E14.Cry.for.Help.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61555, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/c1490fb5-ec2d-4af1-a1c5-285cbd034d1d", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E14.720p.BluRay.x264-TAXES", "episode_id": 61555, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/37001ae6-84f1-4e3d-bb91-c72ce7edc4c7", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E14.1080p.BluRay.x264-TAXES", "episode_id": 61555, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/8e62833f-1e6e-4dbc-b314-1bbd8fd23c3b", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E14.1080p.BluRay.x264-TAXES", "episode_id": 61555, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/c2b74752-fa5c-4e31-996b-4bd2fe78fafd", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E15.Keystone.Motel.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61556, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/64feb902-0fbc-43a9-92b6-ca791d5af9d6", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E15.720p.BluRay.x264-TAXES", "episode_id": 61556, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/6b184af7-69fd-42c0-98b1-049da7acce66", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E15.1080p.BluRay.x264-TAXES", "episode_id": 61556, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/aa62b0fe-4d3c-4a39-b042-30250bd53480", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E15.1080p.BluRay.x264-TAXES", "episode_id": 61556, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/2e31f494-75c2-4b70-834f-9ad51ff789c9", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E16.Onion.Friend.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61557, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/79a454b9-d767-4b2a-a6e9-9a2ee70702d7", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E16.720p.BluRay.x264-TAXES", "episode_id": 61557, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/c9313ff8-b3b7-4704-ae35-e2c9cff93793", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E16.1080p.BluRay.x264-TAXES", "episode_id": 61557, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/11abb4fa-e702-4b0b-9fe8-1293b05749e9", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E16.1080p.BluRay.x264-TAXES", "episode_id": 61557, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/69b82c62-a7cb-4884-9346-fb8b74bbc298", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E17.Historical.Friction.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61558, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/2e1fceb2-f278-47d2-a730-66889dcefe67", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E17.720p.BluRay.x264-TAXES", "episode_id": 61558, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/8162b674-7268-4b7a-8e94-5870eba92a5c", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E17.1080p.BluRay.x264-TAXES", "episode_id": 61558, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/d29c7bb8-3df8-4f79-9aa1-e3201631795e", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E17.1080p.BluRay.x264-TAXES", "episode_id": 61558, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/8797153d-080a-42e6-92bc-29553e7268b4", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E18.Friend.Ship.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61559, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/b1d23421-25c3-44e9-8106-5726da6c6dad", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E18.720p.BluRay.x264-TAXES", "episode_id": 61559, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/74dc4287-368e-4ada-9820-6f94605405b9", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E18.1080p.BluRay.x264-TAXES", "episode_id": 61559, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/d8196e35-9ca4-402a-8c62-ac13f5f6cb0b", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E18.1080p.BluRay.x264-TAXES", "episode_id": 61559, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/40857ce6-a190-4974-9003-35b8db7d85d7", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E19.Nightmare.Hospital.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61560, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/c89642d0-0598-4844-87dc-ec70becefcaf", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E19.720p.BluRay.x264-TAXES", "episode_id": 61560, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/25f00635-c203-4d6e-9d50-c701a6492d01", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E19.1080p.BluRay.x264-TAXES", "episode_id": 61560, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/2bf735da-74a2-4eca-a7e3-ff3a4ba0b989", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E19.1080p.BluRay.x264-TAXES", "episode_id": 61560, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/b586e478-ddf1-41ca-a58c-df5f6bb5ef40", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E20.Sadies.Song.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61561, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/f0c6905e-ee0f-4991-8e3c-809e3c22a358", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E20.720p.BluRay.x264-TAXES", "episode_id": 61561, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/9e07eb80-9951-4ee9-b390-d76628ca724f", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E20.1080p.BluRay.x264-TAXES", "episode_id": 61561, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/791638a3-688c-4a0a-951c-96c0c829220a", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E20.1080p.BluRay.x264-TAXES", "episode_id": 61561, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/e0ac62a6-b75f-4e57-90fc-9beb9c0fd30d", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E21.Catch.and.Release.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61562, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/0843e4d5-6c87-4cd8-a8b5-d8e6e13d192f", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E21.720p.BluRay.x264-TAXES", "episode_id": 61562, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/9aecca3a-6b91-45ad-adc9-6b0a2011436e", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E21.1080p.BluRay.x264-TAXES", "episode_id": 61562, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/4201a5d3-3d2f-4822-8296-9e4122e26a93", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E21.1080p.BluRay.x264-TAXES", "episode_id": 61562, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/25d66276-ad37-4e0b-98ae-c7fe8f0c7121", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E22.When.It.Rains.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61563, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/05eebe11-d55f-45b4-b59c-a814cbe35b62", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E22.720p.BluRay.x264-TAXES", "episode_id": 61563, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/b55262a1-6e73-4dbb-a1ae-93394faa94b7", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E22.1080p.BluRay.x264-TAXES", "episode_id": 61563, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/67aacbc8-8ea2-430d-b00e-e0ec769512c9", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E22.1080p.BluRay.x264-TAXES", "episode_id": 61563, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/67fe89f6-5989-4182-bf14-7065a9cf3650", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E23.Back.to.the.Barn.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61564, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/4e229560-0e8e-46d7-8498-8dee39bd977c", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E23.720p.BluRay.x264-TAXES", "episode_id": 61564, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/511d5261-21ef-4062-a9e0-4e1db6dafa7d", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E23.1080p.BluRay.x264-TAXES", "episode_id": 61564, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/222f76a8-10b3-45a0-8483-4899d2c4946d", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E23.1080p.BluRay.x264-TAXES", "episode_id": 61564, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/af2750ca-4dc9-4613-a3ba-8ee03d5cb561", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E24.Too.Far.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61565, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/6e396995-e20d-4dbf-ab31-656aa5c56959", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E24.720p.BluRay.x264-TAXES", "episode_id": 61565, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/2c100342-4553-430f-a0ae-cdee7efc6524", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E24.1080p.BluRay.x264-TAXES", "episode_id": 61565, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/2da62b64-3bf0-4092-a3d5-ab68e49e548e", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E24.1080p.BluRay.x264-TAXES", "episode_id": 61565, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/6f2223b1-6c77-41f9-9e6e-394617e761bf", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E25.The.Answer.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61566, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/df2ab300-f84e-4838-8983-84a711299538", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E25.720p.BluRay.x264-TAXES", "episode_id": 61566, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/3b707c24-342f-4b92-b6b0-d40e00ccbbf5", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E25.1080p.BluRay.x264-TAXES", "episode_id": 61566, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/26ba16bf-8c33-4d7f-8d28-d641cdb33172", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E25.1080p.BluRay.x264-TAXES", "episode_id": 61566, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/e88445d1-97e7-4e3a-af2c-9dbeb8efc2fe", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E26.Stevens.Birthday.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61567, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/30ab68a0-2d7d-47de-8311-7b9138f7aabe", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E26.720p.BluRay.x264-TAXES", "episode_id": 61567, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/dd6a022b-e670-4d95-8fcf-7a607a5d1cc1", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E26.1080p.BluRay.x264-TAXES", "episode_id": 61567, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": *********, "cached": true}, {"guid": "https://nzbfinder.ws/details/a2132753-c942-497d-830c-5a3eaa3716f9", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E26.1080p.BluRay.x264-TAXES", "episode_id": 61567, "decision": "ACCEPT", "risk_score": 0.132176252898765, "probe_missing_ratio": 0.003816793893129771, "size": 17338582670, "cached": true}], "packs": [], "best": {"guid": "https://nzbfinder.ws/details/d1a6dff5-57c6-4bff-970a-f94fcb822a64", "indexer": "NZBFinder (Prowlarr)", "title": "Steven.Universe.S02E01.Full.Disclosure.1080p.BluRay.Opus.2.0.x265-edge2020", "episode_id": 61539, "decision": "ACCEPT", "risk_score": 0.021783333956386187, "probe_missing_ratio": 0.0, "size": *********, "cached": false}, "strategy": "episodes", "plan": {"episodes": ["https://nzbfinder.ws/details/d1a6dff5-57c6-4bff-970a-f94fcb822a64", "https://nzbfinder.ws/details/3a80879a-4090-4f98-9f80-4e33afae9e6f", "https://nzbfinder.ws/details/dc3d9885-c884-4c12-b193-75f8ec4e02ea", "https://nzbfinder.ws/details/9a43bf0d-543b-42b1-925f-54a3399efdbe", "https://nzbfinder.ws/details/d161e5f9-f084-4e6b-9951-f4d5bd8952c2", "https://nzbfinder.ws/details/5f693bce-75f7-477b-8336-8a57a4d3e047", "https://nzbfinder.ws/details/3969f110-4228-4e62-8151-0e5a3247ae68", "https://nzbfinder.ws/details/70f149b1-98b7-478b-849c-a97d76762fb0", "https://nzbfinder.ws/details/c13f086d-19e9-47f8-adf3-de85d9418a10", "https://nzbfinder.ws/details/09c5ccb8-7218-4c9b-bf4e-d7a4869c2806", "https://nzbfinder.ws/details/79e1b31e-b685-4c9a-8b39-cf2d3fafbecc", "https://nzbfinder.ws/details/7bce1352-49e8-459d-8653-fbe33b3ebe0c", "https://nzbfinder.ws/details/969cadf5-f9a3-49b7-8e0d-bbcd19a390e9", "https://nzbfinder.ws/details/9fc8f7e8-2bae-41f0-8964-0e3c342c8ce9", "https://nzbfinder.ws/details/943708eb-2aad-46bd-b5a4-0de07eabf051", "https://nzbfinder.ws/details/3c4697b1-e729-493e-8ada-7f6b1ffcde10", "https://nzbfinder.ws/details/8e62833f-1e6e-4dbc-b314-1bbd8fd23c3b", "https://nzbfinder.ws/details/aa62b0fe-4d3c-4a39-b042-30250bd53480", "https://nzbfinder.ws/details/11abb4fa-e702-4b0b-9fe8-1293b05749e9", "https://nzbfinder.ws/details/d29c7bb8-3df8-4f79-9aa1-e3201631795e", "https://nzbfinder.ws/details/d8196e35-9ca4-402a-8c62-ac13f5f6cb0b", "https://nzbfinder.ws/details/2bf735da-74a2-4eca-a7e3-ff3a4ba0b989", "https://nzbfinder.ws/details/791638a3-688c-4a0a-951c-96c0c829220a", "https://nzbfinder.ws/details/4201a5d3-3d2f-4822-8296-9e4122e26a93", "https://nzbfinder.ws/details/67aacbc8-8ea2-430d-b00e-e0ec769512c9", "https://nzbfinder.ws/details/222f76a8-10b3-45a0-8483-4899d2c4946d", "https://nzbfinder.ws/details/2da62b64-3bf0-4092-a3d5-ab68e49e548e", "https://nzbfinder.ws/details/26ba16bf-8c33-4d7f-8d28-d641cdb33172", "https://nzbfinder.ws/details/a2132753-c942-497d-830c-5a3eaa3716f9"], "pack": null}, "stats": {"episodes_total": 29, "episodes_covered": 29, "episodes_accepted_files": 110, "best_files_per_episode": 29, "packs_analyzed": 0, "packs_accepted": 0, "accept_fraction": 1.0, "cache_hits": 106, "cache_hit_rate": 0.9636363636363636, "total_analyses": 110, "total_time": 70.11518359184265}}