=== TERMINAL OUTPUT LOG ===
Script: 02_download_and_organize
Started: 2025-09-18 14:45:08
Log File: C:\Users\<USER>\Videos\PlexAutomator\logs\02_download_and_organize_2025-09-18_02-45-08-PM.txt
==================================================

[2025-09-18 14:45:08] [STDOUT] [+0:00:00] 📝 Terminal logging started for 02_download_and_organize
[2025-09-18 14:45:08] [STDOUT] [+0:00:00] 
[2025-09-18 14:45:08] [STDOUT] [+0:00:00] 📄 Log file: C:\Users\<USER>\Videos\PlexAutomator\logs\02_download_and_organize_2025-09-18_02-45-08-PM.txt
[2025-09-18 14:45:08] [STDOUT] [+0:00:00] 
[2025-09-18 14:45:08] [STDOUT] [+0:00:00] 🕐 Started at: 2025-09-18 14:45:08
[2025-09-18 14:45:08] [STDOUT] [+0:00:00] 
[2025-09-18 14:45:08] [STDOUT] [+0:00:00] ------------------------------------------------------------
[2025-09-18 14:45:08] [STDOUT] [+0:00:00] 
[2025-09-18 14:45:08] [STDOUT] [+0:00:00] *** UNIFIED Stage 02: Download and Organize ***
[2025-09-18 14:45:08] [STDOUT] [+0:00:00] 
[2025-09-18 14:45:08] [STDOUT] [+0:00:00] ==================================================
[2025-09-18 14:45:08] [STDOUT] [+0:00:00] 
[2025-09-18 14:45:08] [STDOUT] [+0:00:00] + Consolidated from multiple O2 scripts into one unified implementation
[2025-09-18 14:45:08] [STDOUT] [+0:00:00] 
[2025-09-18 14:45:08] [STDOUT] [+0:00:00] >> Modern Radarr API integration
[2025-09-18 14:45:08] [STDOUT] [+0:00:00] 
[2025-09-18 14:45:08] [STDOUT] [+0:00:00] -- Simplified workflow: Radarr -> SABnzbd -> Plex
[2025-09-18 14:45:08] [STDOUT] [+0:00:00] 
[2025-09-18 14:45:08] [STDOUT] [+0:00:00] >> Clean, maintainable codebase
[2025-09-18 14:45:08] [STDOUT] [+0:00:00] 
[2025-09-18 14:45:08] [STDOUT] [+0:00:00]    Default: Interactive mode (use --movies-only, --tv-only, or --all for command-line mode)
[2025-09-18 14:45:08] [STDOUT] [+0:00:00] 
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,898 - pipeline_02 - INFO - ===== Starting Pipeline 02 Execution =====
[2025-09-18 14:45:08] [STDOUT] [+0:00:00] INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
[2025-09-18 14:45:08] [STDOUT] [+0:00:00] 
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,900 - pipeline_02 - INFO - Settings loaded successfully
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,901 - pipeline_02 - INFO - Command-line mode: Processing tv_shows
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,901 - pipeline_02 - INFO - 📺 Starting Sonarr (TV Shows) monitoring...
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,901 - pipeline_02 - INFO - ===== Starting Modern Sonarr TV Show Download Monitoring with SQLite =====
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,901 - pipeline_02 - INFO -      ENHANCED: Dual-detection system (Filesystem + Sonarr API) + SQLite state
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,902 - _internal.utils.filesystem_first_state_manager - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,902 - pipeline_02 - INFO - Discovering TV shows by scanning filesystem...
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,904 - pipeline_02 - INFO - Found 0 content items across 14 stages
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,904 - pipeline_02 - INFO -      SABnzbd complete directory: workspace\1_downloading\complete_raw
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,904 - pipeline_02 - INFO -      Sonarr API endpoint: http://localhost:8989
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,904 - pipeline_02 - INFO -      SMART STATE VALIDATION: Checking for inconsistent TV show states...
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,911 - pipeline_02 - INFO - Retrieved 2 TV series from Sonarr
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,912 - pipeline_02 - INFO -      Active TV show downloads in Sonarr queue: 10
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,913 - pipeline_02 - INFO -      ENHANCED: Checking both Sonarr API and filesystem for completed TV shows
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,913 - pipeline_02 - INFO -      Will scan SABnzbd directory: workspace\1_downloading\complete_raw
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,914 - pipeline_02 - INFO - Selected main video file: Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (0.83 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,914 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~0.83 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,914 - pipeline_02 - INFO - Selected main video file: Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (0.88 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,914 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~0.88 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,915 - pipeline_02 - INFO - Selected main video file: Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (0.84 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,915 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~0.84 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,915 - pipeline_02 - INFO - Selected main video file: Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (0.83 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,915 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~0.83 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,915 - pipeline_02 - INFO - Selected main video file: Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (0.94 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,915 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~0.94 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,916 - pipeline_02 - INFO - Selected main video file: Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (0.99 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,916 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~0.99 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,916 - pipeline_02 - INFO - Selected main video file: Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (0.87 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,916 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~0.87 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,916 - pipeline_02 - INFO - Selected main video file: Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (0.94 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,916 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~0.94 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,917 - pipeline_02 - INFO - Selected main video file: Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (1.02 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,917 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~1.02 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,917 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S01E47.720p.BluRay.x264-TAXES.mkv (0.21 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,917 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S01E47.720p.BluRay.x264-TAXES (1 video files, ~0.21 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,918 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S01E48.720p.BluRay.x264-TAXES.mkv (0.21 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,918 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S01E48.720p.BluRay.x264-TAXES (1 video files, ~0.21 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,918 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E11.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,918 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E11.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,918 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E12.1080p.BluRay.x264-TAXES.mkv (0.55 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,918 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E12.1080p.BluRay.x264-TAXES (1 video files, ~0.55 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,919 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E13.1080p.BluRay.x264-TAXES.mkv (0.55 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,919 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E13.1080p.BluRay.x264-TAXES (1 video files, ~0.55 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,919 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E14.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,919 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E14.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,919 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E15.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,919 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E15.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,920 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E16.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,920 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E16.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,920 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E17.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,920 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E17.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,920 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E18.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,920 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E18.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,921 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E19.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,921 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E19.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,921 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E20.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,921 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E20.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,921 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E21.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,921 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E21.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,922 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E22.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,922 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E22.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,922 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E23.1080p.BluRay.x264-TAXES.mkv (0.55 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,922 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E23.1080p.BluRay.x264-TAXES (1 video files, ~0.55 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,922 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E24.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,922 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E24.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,922 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E25.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,922 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E25.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,945 - pipeline_02 - INFO -      FILESYSTEM DETECTION: Found 26 completed TV folders ready for organization
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,945 - pipeline_02 - INFO -      Processing 26 completed TV folders
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,946 - pipeline_02 - INFO -      🔒 Acquired lock for Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso, processing...
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,946 - pipeline_02 - INFO -      Organizing completed TV folder: Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,946 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,946 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,946 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Futurama', 'year': None, 'original_folder': 'Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso', 'confidence': 0.95}
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,947 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,947 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): name 'detect_content_type_from_name' is not defined
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,947 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso'
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,952 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Futurama'
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,952 - pipeline_02 - INFO - 📡 Using official title 'Futurama' from Radarr
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,952 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Futurama', year='N/A', type='tv_show'
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,952 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,952 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,989 - pipeline_02 - INFO - Detected resolution 1920x1080 for Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,989 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 14:45:08] [STDERR] [+0:00:00] 2025-09-18 14:45:08,990 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,598 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 1999
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,599 - pipeline_02 - INFO - Created directory: workspace\2_downloaded_and_organized\tv_shows\1080p\Futurama (1999)\Season 01
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,600 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso\Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Futurama (1999)\Season 01\S01E01.mkv'
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,600 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,601 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (ignore_errors=True)
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,602 - pipeline_02 - INFO - 📝 Updated .organized - S01E01 with metadata
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,602 - pipeline_02 - INFO - ✅ Episode-aware marker set for S01E01
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,603 - pipeline_02 - INFO -      🔓 Released lock for Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,603 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,605 - pipeline_02 - INFO -    Checking 10 queue items for pending episodes...
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,605 - pipeline_02 - INFO -    No pending episodes found - safe to cleanup
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,606 - pipeline_02 - INFO -      🧹 All episodes complete - running Sonarr cleanup
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,606 - pipeline_02 - INFO - 🧹 Starting enhanced Sonarr cleanup for: Futurama
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,606 - pipeline_02 - INFO -    Available IDs: sonarr_id=None, tvdb_id=None, year=None
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,606 - pipeline_02 - INFO - 🔍 Step 1: Getting Sonarr series and queue...
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,609 - pipeline_02 - INFO -    Retrieved 2 series from Sonarr
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,611 - pipeline_02 - INFO -    Retrieved 10 queue items from Sonarr
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,611 - pipeline_02 - INFO - 🎯 Step 2: Finding TV series using multiple matching strategies...
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,611 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,611 - pipeline_02 - INFO -    Found 3 additional queue items by title matching
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,611 - pipeline_02 - INFO -    Removing 3 queue items...
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,626 - pipeline_02 - INFO -    ✅ Removed queue item: Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (ID: 858702967)
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,640 - pipeline_02 - INFO -    ✅ Removed queue item: Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (ID: 1614998198)
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,653 - pipeline_02 - INFO -    ✅ Removed queue item: Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (ID: 683321369)
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,654 - pipeline_02 - INFO -    Series not found in Sonarr library - cleanup complete
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,654 - pipeline_02 - INFO - 🎉 Enhanced Sonarr cleanup completed successfully for: Futurama
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,654 - pipeline_02 - INFO -      🔒 Acquired lock for Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso, processing...
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,654 - pipeline_02 - INFO -      Organizing completed TV folder: Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,654 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,654 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,655 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Futurama', 'year': None, 'original_folder': 'Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso', 'confidence': 0.95}
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,655 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,655 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): name 'detect_content_type_from_name' is not defined
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,655 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso'
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,659 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Futurama'
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,659 - pipeline_02 - INFO - 📡 Using official title 'Futurama' from Radarr
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,659 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Futurama', year='N/A', type='tv_show'
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,659 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,659 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,688 - pipeline_02 - INFO - Detected resolution 1920x1080 for Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,688 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 14:45:09] [STDERR] [+0:00:00] 2025-09-18 14:45:09,688 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,137 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 1999
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,138 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso\Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Futurama (1999)\Season 01\S01E02.mkv'
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,138 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,139 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (ignore_errors=True)
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,139 - pipeline_02 - INFO - 📝 Updated .organized - S01E02 with metadata
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,139 - pipeline_02 - INFO - ✅ Episode-aware marker set for S01E02
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,140 - pipeline_02 - INFO -      🔓 Released lock for Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,140 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,143 - pipeline_02 - INFO -    Checking 10 queue items for pending episodes...
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,143 - pipeline_02 - INFO -    No pending episodes found - safe to cleanup
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,143 - pipeline_02 - INFO -      🧹 All episodes complete - running Sonarr cleanup
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,143 - pipeline_02 - INFO - 🧹 Starting enhanced Sonarr cleanup for: Futurama
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,143 - pipeline_02 - INFO -    Available IDs: sonarr_id=None, tvdb_id=None, year=None
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,144 - pipeline_02 - INFO - 🔍 Step 1: Getting Sonarr series and queue...
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,147 - pipeline_02 - INFO -    Retrieved 2 series from Sonarr
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,149 - pipeline_02 - INFO -    Retrieved 10 queue items from Sonarr
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,149 - pipeline_02 - INFO - 🎯 Step 2: Finding TV series using multiple matching strategies...
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,149 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,149 - pipeline_02 - INFO -    Found 0 additional queue items by title matching
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,149 - pipeline_02 - INFO -    No queue items found to remove
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,149 - pipeline_02 - INFO -    Series not found in Sonarr library - cleanup complete
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,150 - pipeline_02 - INFO - 🎉 Enhanced Sonarr cleanup completed successfully for: Futurama
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,150 - pipeline_02 - INFO -      🔒 Acquired lock for Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso, processing...
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,150 - pipeline_02 - INFO -      Organizing completed TV folder: Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,150 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,150 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,150 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Futurama', 'year': None, 'original_folder': 'Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso', 'confidence': 0.95}
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,150 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,151 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): name 'detect_content_type_from_name' is not defined
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,151 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso'
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,154 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Futurama'
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,154 - pipeline_02 - INFO - 📡 Using official title 'Futurama' from Radarr
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,154 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Futurama', year='N/A', type='tv_show'
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,154 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,154 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,184 - pipeline_02 - INFO - Detected resolution 1920x1080 for Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,185 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,185 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,547 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 1999
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,548 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso\Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Futurama (1999)\Season 01\S01E03.mkv'
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,548 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,548 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (ignore_errors=True)
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,549 - pipeline_02 - INFO - 📝 Updated .organized - S01E03 with metadata
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,549 - pipeline_02 - INFO - ✅ Episode-aware marker set for S01E03
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,550 - pipeline_02 - INFO -      🔓 Released lock for Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,550 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,552 - pipeline_02 - INFO -    Checking 10 queue items for pending episodes...
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,552 - pipeline_02 - INFO -    No pending episodes found - safe to cleanup
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,552 - pipeline_02 - INFO -      🧹 All episodes complete - running Sonarr cleanup
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,553 - pipeline_02 - INFO - 🧹 Starting enhanced Sonarr cleanup for: Futurama
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,553 - pipeline_02 - INFO -    Available IDs: sonarr_id=None, tvdb_id=None, year=None
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,553 - pipeline_02 - INFO - 🔍 Step 1: Getting Sonarr series and queue...
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,556 - pipeline_02 - INFO -    Retrieved 2 series from Sonarr
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,557 - pipeline_02 - INFO -    Retrieved 10 queue items from Sonarr
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,558 - pipeline_02 - INFO - 🎯 Step 2: Finding TV series using multiple matching strategies...
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,558 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,558 - pipeline_02 - INFO -    Found 0 additional queue items by title matching
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,558 - pipeline_02 - INFO -    No queue items found to remove
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,558 - pipeline_02 - INFO -    Series not found in Sonarr library - cleanup complete
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,558 - pipeline_02 - INFO - 🎉 Enhanced Sonarr cleanup completed successfully for: Futurama
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,559 - pipeline_02 - INFO -      🔒 Acquired lock for Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso, processing...
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,559 - pipeline_02 - INFO -      Organizing completed TV folder: Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,559 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,559 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,559 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Futurama', 'year': None, 'original_folder': 'Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso', 'confidence': 0.95}
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,559 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,559 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): name 'detect_content_type_from_name' is not defined
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,559 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso'
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,563 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Futurama'
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,563 - pipeline_02 - INFO - 📡 Using official title 'Futurama' from Radarr
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,563 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Futurama', year='N/A', type='tv_show'
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,563 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,563 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,594 - pipeline_02 - INFO - Detected resolution 1920x1080 for Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,594 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,594 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,824 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 1999
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,826 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso\Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Futurama (1999)\Season 01\S01E04.mkv'
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,826 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,826 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (ignore_errors=True)
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,827 - pipeline_02 - INFO - 📝 Updated .organized - S01E04 with metadata
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,827 - pipeline_02 - INFO - ✅ Episode-aware marker set for S01E04
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,828 - pipeline_02 - INFO -      🔓 Released lock for Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,828 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,830 - pipeline_02 - INFO -    Checking 10 queue items for pending episodes...
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,830 - pipeline_02 - INFO -    No pending episodes found - safe to cleanup
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,831 - pipeline_02 - INFO -      🧹 All episodes complete - running Sonarr cleanup
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,831 - pipeline_02 - INFO - 🧹 Starting enhanced Sonarr cleanup for: Futurama
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,831 - pipeline_02 - INFO -    Available IDs: sonarr_id=None, tvdb_id=None, year=None
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,831 - pipeline_02 - INFO - 🔍 Step 1: Getting Sonarr series and queue...
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,834 - pipeline_02 - INFO -    Retrieved 2 series from Sonarr
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,835 - pipeline_02 - INFO -    Retrieved 10 queue items from Sonarr
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,835 - pipeline_02 - INFO - 🎯 Step 2: Finding TV series using multiple matching strategies...
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,835 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,835 - pipeline_02 - INFO -    Found 0 additional queue items by title matching
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,835 - pipeline_02 - INFO -    No queue items found to remove
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,835 - pipeline_02 - INFO -    Series not found in Sonarr library - cleanup complete
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,835 - pipeline_02 - INFO - 🎉 Enhanced Sonarr cleanup completed successfully for: Futurama
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,836 - pipeline_02 - INFO -      🔒 Acquired lock for Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso, processing...
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,836 - pipeline_02 - INFO -      Organizing completed TV folder: Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,836 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,836 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,837 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Futurama', 'year': None, 'original_folder': 'Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso', 'confidence': 0.95}
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,837 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,837 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): name 'detect_content_type_from_name' is not defined
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,837 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso'
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,841 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Futurama'
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,841 - pipeline_02 - INFO - 📡 Using official title 'Futurama' from Radarr
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,841 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Futurama', year='N/A', type='tv_show'
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,841 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,841 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,872 - pipeline_02 - INFO - Detected resolution 1920x1080 for Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,872 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 14:45:10] [STDERR] [+0:00:01] 2025-09-18 14:45:10,872 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,175 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 1999
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,176 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso\Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Futurama (1999)\Season 01\S01E05.mkv'
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,176 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,177 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (ignore_errors=True)
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,177 - pipeline_02 - INFO - 📝 Updated .organized - S01E05 with metadata
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,177 - pipeline_02 - INFO - ✅ Episode-aware marker set for S01E05
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,178 - pipeline_02 - INFO -      🔓 Released lock for Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,178 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,181 - pipeline_02 - INFO -    Checking 10 queue items for pending episodes...
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,181 - pipeline_02 - INFO -    No pending episodes found - safe to cleanup
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,181 - pipeline_02 - INFO -      🧹 All episodes complete - running Sonarr cleanup
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,181 - pipeline_02 - INFO - 🧹 Starting enhanced Sonarr cleanup for: Futurama
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,181 - pipeline_02 - INFO -    Available IDs: sonarr_id=None, tvdb_id=None, year=None
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,181 - pipeline_02 - INFO - 🔍 Step 1: Getting Sonarr series and queue...
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,184 - pipeline_02 - INFO -    Retrieved 2 series from Sonarr
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,185 - pipeline_02 - INFO -    Retrieved 10 queue items from Sonarr
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,185 - pipeline_02 - INFO - 🎯 Step 2: Finding TV series using multiple matching strategies...
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,186 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,186 - pipeline_02 - INFO -    Found 0 additional queue items by title matching
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,186 - pipeline_02 - INFO -    No queue items found to remove
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,186 - pipeline_02 - INFO -    Series not found in Sonarr library - cleanup complete
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,186 - pipeline_02 - INFO - 🎉 Enhanced Sonarr cleanup completed successfully for: Futurama
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,186 - pipeline_02 - INFO -      🔒 Acquired lock for Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso, processing...
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,186 - pipeline_02 - INFO -      Organizing completed TV folder: Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,187 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,187 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,187 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Futurama', 'year': None, 'original_folder': 'Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso', 'confidence': 0.95}
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,187 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,187 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): name 'detect_content_type_from_name' is not defined
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,187 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso'
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,190 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Futurama'
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,190 - pipeline_02 - INFO - 📡 Using official title 'Futurama' from Radarr
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,190 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Futurama', year='N/A', type='tv_show'
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,190 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,190 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,221 - pipeline_02 - INFO - Detected resolution 1920x1080 for Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,221 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,221 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,477 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 1999
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,478 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso\Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Futurama (1999)\Season 01\S01E06.mkv'
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,478 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,479 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (ignore_errors=True)
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,479 - pipeline_02 - INFO - 📝 Updated .organized - S01E06 with metadata
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,479 - pipeline_02 - INFO - ✅ Episode-aware marker set for S01E06
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,480 - pipeline_02 - INFO -      🔓 Released lock for Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,480 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,483 - pipeline_02 - INFO -    Checking 10 queue items for pending episodes...
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,483 - pipeline_02 - INFO -    No pending episodes found - safe to cleanup
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,483 - pipeline_02 - INFO -      🧹 All episodes complete - running Sonarr cleanup
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,483 - pipeline_02 - INFO - 🧹 Starting enhanced Sonarr cleanup for: Futurama
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,483 - pipeline_02 - INFO -    Available IDs: sonarr_id=None, tvdb_id=None, year=None
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,483 - pipeline_02 - INFO - 🔍 Step 1: Getting Sonarr series and queue...
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,487 - pipeline_02 - INFO -    Retrieved 2 series from Sonarr
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,489 - pipeline_02 - INFO -    Retrieved 10 queue items from Sonarr
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,489 - pipeline_02 - INFO - 🎯 Step 2: Finding TV series using multiple matching strategies...
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,490 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,490 - pipeline_02 - INFO -    Found 0 additional queue items by title matching
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,490 - pipeline_02 - INFO -    No queue items found to remove
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,490 - pipeline_02 - INFO -    Series not found in Sonarr library - cleanup complete
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,490 - pipeline_02 - INFO - 🎉 Enhanced Sonarr cleanup completed successfully for: Futurama
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,491 - pipeline_02 - INFO -      🔒 Acquired lock for Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso, processing...
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,491 - pipeline_02 - INFO -      Organizing completed TV folder: Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,491 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,491 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,491 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Futurama', 'year': None, 'original_folder': 'Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso', 'confidence': 0.95}
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,491 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,491 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): name 'detect_content_type_from_name' is not defined
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,491 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso'
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,497 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Futurama'
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,497 - pipeline_02 - INFO - 📡 Using official title 'Futurama' from Radarr
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,497 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Futurama', year='N/A', type='tv_show'
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,497 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,497 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,530 - pipeline_02 - INFO - Detected resolution 1920x1080 for Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,530 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,530 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,739 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 1999
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,740 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso\Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Futurama (1999)\Season 01\S01E07.mkv'
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,740 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,740 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (ignore_errors=True)
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,741 - pipeline_02 - INFO - 📝 Updated .organized - S01E07 with metadata
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,741 - pipeline_02 - INFO - ✅ Episode-aware marker set for S01E07
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,742 - pipeline_02 - INFO -      🔓 Released lock for Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,742 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,744 - pipeline_02 - INFO -    Checking 10 queue items for pending episodes...
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,744 - pipeline_02 - INFO -    No pending episodes found - safe to cleanup
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,745 - pipeline_02 - INFO -      🧹 All episodes complete - running Sonarr cleanup
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,745 - pipeline_02 - INFO - 🧹 Starting enhanced Sonarr cleanup for: Futurama
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,745 - pipeline_02 - INFO -    Available IDs: sonarr_id=None, tvdb_id=None, year=None
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,745 - pipeline_02 - INFO - 🔍 Step 1: Getting Sonarr series and queue...
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,748 - pipeline_02 - INFO -    Retrieved 2 series from Sonarr
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,750 - pipeline_02 - INFO -    Retrieved 10 queue items from Sonarr
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,750 - pipeline_02 - INFO - 🎯 Step 2: Finding TV series using multiple matching strategies...
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,750 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,750 - pipeline_02 - INFO -    Found 0 additional queue items by title matching
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,750 - pipeline_02 - INFO -    No queue items found to remove
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,750 - pipeline_02 - INFO -    Series not found in Sonarr library - cleanup complete
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,751 - pipeline_02 - INFO - 🎉 Enhanced Sonarr cleanup completed successfully for: Futurama
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,752 - pipeline_02 - INFO -      🔒 Acquired lock for Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso, processing...
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,752 - pipeline_02 - INFO -      Organizing completed TV folder: Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,752 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,752 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,752 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Futurama', 'year': None, 'original_folder': 'Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso', 'confidence': 0.95}
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,752 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,753 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): name 'detect_content_type_from_name' is not defined
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,753 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso'
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,756 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Futurama'
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,756 - pipeline_02 - INFO - 📡 Using official title 'Futurama' from Radarr
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,756 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Futurama', year='N/A', type='tv_show'
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,756 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,756 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,788 - pipeline_02 - INFO - Detected resolution 1920x1080 for Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,788 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 14:45:11] [STDERR] [+0:00:02] 2025-09-18 14:45:11,788 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,040 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 1999
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,041 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso\Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Futurama (1999)\Season 01\S01E08.mkv'
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,041 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,041 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (ignore_errors=True)
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,042 - pipeline_02 - INFO - 📝 Updated .organized - S01E08 with metadata
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,042 - pipeline_02 - INFO - ✅ Episode-aware marker set for S01E08
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,043 - pipeline_02 - INFO -      🔓 Released lock for Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,043 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,045 - pipeline_02 - INFO -    Checking 10 queue items for pending episodes...
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,045 - pipeline_02 - INFO -    No pending episodes found - safe to cleanup
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,046 - pipeline_02 - INFO -      🧹 All episodes complete - running Sonarr cleanup
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,046 - pipeline_02 - INFO - 🧹 Starting enhanced Sonarr cleanup for: Futurama
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,046 - pipeline_02 - INFO -    Available IDs: sonarr_id=None, tvdb_id=None, year=None
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,046 - pipeline_02 - INFO - 🔍 Step 1: Getting Sonarr series and queue...
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,049 - pipeline_02 - INFO -    Retrieved 2 series from Sonarr
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,050 - pipeline_02 - INFO -    Retrieved 10 queue items from Sonarr
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,050 - pipeline_02 - INFO - 🎯 Step 2: Finding TV series using multiple matching strategies...
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,050 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,050 - pipeline_02 - INFO -    Found 0 additional queue items by title matching
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,050 - pipeline_02 - INFO -    No queue items found to remove
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,050 - pipeline_02 - INFO -    Series not found in Sonarr library - cleanup complete
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,051 - pipeline_02 - INFO - 🎉 Enhanced Sonarr cleanup completed successfully for: Futurama
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,051 - pipeline_02 - INFO -      🔒 Acquired lock for Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso, processing...
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,051 - pipeline_02 - INFO -      Organizing completed TV folder: Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,051 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,051 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,051 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Futurama', 'year': None, 'original_folder': 'Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso', 'confidence': 0.95}
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,052 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,052 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): name 'detect_content_type_from_name' is not defined
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,052 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso'
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,055 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Futurama'
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,055 - pipeline_02 - INFO - 📡 Using official title 'Futurama' from Radarr
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,055 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Futurama', year='N/A', type='tv_show'
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,055 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,055 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,088 - pipeline_02 - INFO - Detected resolution 1920x1080 for Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,088 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,088 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,349 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 1999
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,350 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso\Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Futurama (1999)\Season 01\S01E09.mkv'
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,350 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,350 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (ignore_errors=True)
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,351 - pipeline_02 - INFO - 📝 Updated .organized - S01E09 with metadata
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,351 - pipeline_02 - INFO - ✅ Episode-aware marker set for S01E09
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,352 - pipeline_02 - INFO -      🔓 Released lock for Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,352 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,355 - pipeline_02 - INFO -    Checking 10 queue items for pending episodes...
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,355 - pipeline_02 - INFO -    No pending episodes found - safe to cleanup
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,355 - pipeline_02 - INFO -      🧹 All episodes complete - running Sonarr cleanup
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,355 - pipeline_02 - INFO - 🧹 Starting enhanced Sonarr cleanup for: Futurama
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,355 - pipeline_02 - INFO -    Available IDs: sonarr_id=None, tvdb_id=None, year=None
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,356 - pipeline_02 - INFO - 🔍 Step 1: Getting Sonarr series and queue...
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,359 - pipeline_02 - INFO -    Retrieved 2 series from Sonarr
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,360 - pipeline_02 - INFO -    Retrieved 10 queue items from Sonarr
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,360 - pipeline_02 - INFO - 🎯 Step 2: Finding TV series using multiple matching strategies...
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,360 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,360 - pipeline_02 - INFO -    Found 0 additional queue items by title matching
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,361 - pipeline_02 - INFO -    No queue items found to remove
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,361 - pipeline_02 - INFO -    Series not found in Sonarr library - cleanup complete
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,361 - pipeline_02 - INFO - 🎉 Enhanced Sonarr cleanup completed successfully for: Futurama
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,361 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S01E47.720p.BluRay.x264-TAXES, processing...
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,361 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S01E47.720p.BluRay.x264-TAXES
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,361 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,362 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S01E47.720p.BluRay.x264-TAXES
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,362 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '720p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S01E47.720p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,362 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,362 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): name 'detect_content_type_from_name' is not defined
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,362 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S01E47.720p.BluRay.x264-TAXES'
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,366 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,366 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,366 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,366 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,366 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,392 - pipeline_02 - INFO - Detected resolution 1280x720 for Steven.Universe.S01E47.720p.BluRay.x264-TAXES.mkv
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,392 - pipeline_02 - INFO - 🎥 ffprobe detection: 720p (1280x720) from video file analysis
[2025-09-18 14:45:12] [STDERR] [+0:00:03] 2025-09-18 14:45:12,392 - pipeline_02 - INFO - 📊 Final resolution determination: 720p (width: 1280px)
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,153 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,154 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S01E47.720p.BluRay.x264-TAXES\Steven.Universe.S01E47.720p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\720p\Steven Universe (2013)\Season 01\S01E47.mkv'
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,154 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,155 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S01E47.720p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,155 - pipeline_02 - INFO - 📝 Updated .organized - S01E47 with metadata
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,155 - pipeline_02 - INFO - ✅ Episode-aware marker set for S01E47
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,156 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S01E47.720p.BluRay.x264-TAXES
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,156 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S01E47.720p.BluRay.x264-TAXES
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,159 - pipeline_02 - INFO -    Checking 10 queue items for pending episodes...
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,160 - pipeline_02 - INFO -    No pending episodes found - safe to cleanup
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,160 - pipeline_02 - INFO -      🧹 All episodes complete - running Sonarr cleanup
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,160 - pipeline_02 - INFO - 🧹 Starting enhanced Sonarr cleanup for: Steven Universe
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,160 - pipeline_02 - INFO -    Available IDs: sonarr_id=None, tvdb_id=None, year=None
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,160 - pipeline_02 - INFO - 🔍 Step 1: Getting Sonarr series and queue...
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,163 - pipeline_02 - INFO -    Retrieved 2 series from Sonarr
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,165 - pipeline_02 - INFO -    Retrieved 10 queue items from Sonarr
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,165 - pipeline_02 - INFO - 🎯 Step 2: Finding TV series using multiple matching strategies...
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,165 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,165 - pipeline_02 - INFO -    Found 10 additional queue items by title matching
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,165 - pipeline_02 - INFO -    Removing 10 queue items...
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,182 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S02E10.1080p.BluRay.x264-TAXES (ID: 1318300110)
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,200 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S02E26.1080p.BluRay.x264-TAXES (ID: 1144228379)
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,217 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S02E08.1080p.BluRay.x264-TAXES (ID: 1696190437)
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,238 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S02E02.1080p.BluRay.x264-TAXES (ID: 1811109108)
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,258 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S02E24.1080p.BluRay.x264-TAXES (ID: *********)
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,278 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S02E22.1080p.BluRay.x264-TAXES (ID: *********)
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,297 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S02E06.1080p.BluRay.x264-TAXES (ID: 1173260786)
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,317 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S02E20.1080p.BluRay.x264-TAXES (ID: 1002193551)
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,335 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S02E01.1080p.BluRay.x264-TAXES (ID: *********)
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,353 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S02E16.1080p.BluRay.x264-TAXES (ID: 1771000838)
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,353 - pipeline_02 - INFO -    Series not found in Sonarr library - cleanup complete
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,354 - pipeline_02 - INFO - 🎉 Enhanced Sonarr cleanup completed successfully for: Steven Universe
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,354 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S01E48.720p.BluRay.x264-TAXES, processing...
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,354 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S01E48.720p.BluRay.x264-TAXES
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,354 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,354 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S01E48.720p.BluRay.x264-TAXES
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,354 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '720p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S01E48.720p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,355 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,355 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): name 'detect_content_type_from_name' is not defined
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,355 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S01E48.720p.BluRay.x264-TAXES'
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,360 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,360 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,361 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,361 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,361 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,388 - pipeline_02 - INFO - Detected resolution 1280x720 for Steven.Universe.S01E48.720p.BluRay.x264-TAXES.mkv
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,388 - pipeline_02 - INFO - 🎥 ffprobe detection: 720p (1280x720) from video file analysis
[2025-09-18 14:45:16] [STDERR] [+0:00:07] 2025-09-18 14:45:16,388 - pipeline_02 - INFO - 📊 Final resolution determination: 720p (width: 1280px)
[2025-09-18 14:45:17] [STDERR] [+0:00:09] 2025-09-18 14:45:17,932 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 14:45:17] [STDERR] [+0:00:09] 2025-09-18 14:45:17,933 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S01E48.720p.BluRay.x264-TAXES\Steven.Universe.S01E48.720p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\720p\Steven Universe (2013)\Season 01\S01E48.mkv'
[2025-09-18 14:45:17] [STDERR] [+0:00:09] 2025-09-18 14:45:17,933 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 14:45:17] [STDERR] [+0:00:09] 2025-09-18 14:45:17,933 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S01E48.720p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 14:45:17] [STDERR] [+0:00:09] 2025-09-18 14:45:17,934 - pipeline_02 - INFO - 📝 Updated .organized - S01E48 with metadata
[2025-09-18 14:45:17] [STDERR] [+0:00:09] 2025-09-18 14:45:17,934 - pipeline_02 - INFO - ✅ Episode-aware marker set for S01E48
[2025-09-18 14:45:17] [STDERR] [+0:00:09] 2025-09-18 14:45:17,935 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S01E48.720p.BluRay.x264-TAXES
[2025-09-18 14:45:17] [STDERR] [+0:00:09] 2025-09-18 14:45:17,935 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S01E48.720p.BluRay.x264-TAXES
[2025-09-18 14:45:17] [STDERR] [+0:00:09] 2025-09-18 14:45:17,938 - pipeline_02 - INFO -    Checking 10 queue items for pending episodes...
[2025-09-18 14:45:17] [STDERR] [+0:00:09] 2025-09-18 14:45:17,938 - pipeline_02 - INFO -    No pending episodes found - safe to cleanup
[2025-09-18 14:45:17] [STDERR] [+0:00:09] 2025-09-18 14:45:17,938 - pipeline_02 - INFO -      🧹 All episodes complete - running Sonarr cleanup
[2025-09-18 14:45:17] [STDERR] [+0:00:09] 2025-09-18 14:45:17,938 - pipeline_02 - INFO - 🧹 Starting enhanced Sonarr cleanup for: Steven Universe
[2025-09-18 14:45:17] [STDERR] [+0:00:09] 2025-09-18 14:45:17,938 - pipeline_02 - INFO -    Available IDs: sonarr_id=None, tvdb_id=None, year=None
[2025-09-18 14:45:17] [STDERR] [+0:00:09] 2025-09-18 14:45:17,939 - pipeline_02 - INFO - 🔍 Step 1: Getting Sonarr series and queue...
[2025-09-18 14:45:17] [STDERR] [+0:00:09] 2025-09-18 14:45:17,942 - pipeline_02 - INFO -    Retrieved 2 series from Sonarr
[2025-09-18 14:45:17] [STDERR] [+0:00:09] 2025-09-18 14:45:17,945 - pipeline_02 - INFO -    Retrieved 10 queue items from Sonarr
[2025-09-18 14:45:17] [STDERR] [+0:00:09] 2025-09-18 14:45:17,945 - pipeline_02 - INFO - 🎯 Step 2: Finding TV series using multiple matching strategies...
[2025-09-18 14:45:17] [STDERR] [+0:00:09] 2025-09-18 14:45:17,945 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-18 14:45:17] [STDERR] [+0:00:09] 2025-09-18 14:45:17,945 - pipeline_02 - INFO -    Found 9 additional queue items by title matching
[2025-09-18 14:45:17] [STDERR] [+0:00:09] 2025-09-18 14:45:17,945 - pipeline_02 - INFO -    Removing 9 queue items...
[2025-09-18 14:45:17] [STDERR] [+0:00:09] 2025-09-18 14:45:17,967 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S02E17.1080p.BluRay.x264-TAXES (ID: 1616884619)
[2025-09-18 14:45:17] [STDERR] [+0:00:09] 2025-09-18 14:45:17,984 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S02E03.1080p.BluRay.x264-TAXES (ID: 1647420958)
[2025-09-18 14:45:18] [STDERR] [+0:00:09] 2025-09-18 14:45:18,004 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S02E25.1080p.BluRay.x264-TAXES (ID: 1892104943)
[2025-09-18 14:45:18] [STDERR] [+0:00:09] 2025-09-18 14:45:18,024 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S02E23.1080p.BluRay.x264-TAXES (ID: *********)
[2025-09-18 14:45:18] [STDERR] [+0:00:09] 2025-09-18 14:45:18,041 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S02E07.1080p.BluRay.x264-TAXES (ID: *********)
[2025-09-18 14:45:18] [STDERR] [+0:00:09] 2025-09-18 14:45:18,060 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S01E46.720p.BluRay.x264-TAXES (ID: 8330230)
[2025-09-18 14:45:18] [STDERR] [+0:00:09] 2025-09-18 14:45:18,079 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S02E15.1080p.BluRay.x264-TAXES (ID: *********)
[2025-09-18 14:45:18] [STDERR] [+0:00:09] 2025-09-18 14:45:18,109 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S02E09.1080p.BluRay.x264-TAXES (ID: 1180564088)
[2025-09-18 14:45:18] [STDERR] [+0:00:09] 2025-09-18 14:45:18,139 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S02E14.1080p.BluRay.x264-TAXES (ID: 85714494)
[2025-09-18 14:45:18] [STDERR] [+0:00:09] 2025-09-18 14:45:18,139 - pipeline_02 - INFO -    Series not found in Sonarr library - cleanup complete
[2025-09-18 14:45:18] [STDERR] [+0:00:09] 2025-09-18 14:45:18,140 - pipeline_02 - INFO - 🎉 Enhanced Sonarr cleanup completed successfully for: Steven Universe
[2025-09-18 14:45:18] [STDERR] [+0:00:09] 2025-09-18 14:45:18,140 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E11.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:45:18] [STDERR] [+0:00:09] 2025-09-18 14:45:18,140 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E11.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:18] [STDERR] [+0:00:09] 2025-09-18 14:45:18,140 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:45:18] [STDERR] [+0:00:09] 2025-09-18 14:45:18,140 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E11.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:18] [STDERR] [+0:00:09] 2025-09-18 14:45:18,141 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E11.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:45:18] [STDERR] [+0:00:09] 2025-09-18 14:45:18,141 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:45:18] [STDERR] [+0:00:09] 2025-09-18 14:45:18,141 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): name 'detect_content_type_from_name' is not defined
[2025-09-18 14:45:18] [STDERR] [+0:00:09] 2025-09-18 14:45:18,141 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S02E11.1080p.BluRay.x264-TAXES'
[2025-09-18 14:45:18] [STDERR] [+0:00:09] 2025-09-18 14:45:18,145 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 14:45:18] [STDERR] [+0:00:09] 2025-09-18 14:45:18,146 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 14:45:18] [STDERR] [+0:00:09] 2025-09-18 14:45:18,146 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 14:45:18] [STDERR] [+0:00:09] 2025-09-18 14:45:18,146 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 14:45:18] [STDERR] [+0:00:09] 2025-09-18 14:45:18,146 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 14:45:18] [STDERR] [+0:00:09] 2025-09-18 14:45:18,173 - pipeline_02 - INFO - Detected resolution 1920x1080 for Steven.Universe.S02E11.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 14:45:18] [STDERR] [+0:00:09] 2025-09-18 14:45:18,173 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 14:45:18] [STDERR] [+0:00:09] 2025-09-18 14:45:18,173 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,349 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,350 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S02E11.1080p.BluRay.x264-TAXES\Steven.Universe.S02E11.1080p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Steven Universe (2013)\Season 02\S02E11.mkv'
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,350 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,351 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S02E11.1080p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,352 - pipeline_02 - INFO - 📝 Updated .organized - S02E11 with metadata
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,352 - pipeline_02 - INFO - ✅ Episode-aware marker set for S02E11
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,353 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E11.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,353 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S02E11.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,355 - pipeline_02 - INFO -    Checking 10 queue items for pending episodes...
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,355 - pipeline_02 - INFO -    No pending episodes found - safe to cleanup
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,355 - pipeline_02 - INFO -      🧹 All episodes complete - running Sonarr cleanup
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,355 - pipeline_02 - INFO - 🧹 Starting enhanced Sonarr cleanup for: Steven Universe
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,355 - pipeline_02 - INFO -    Available IDs: sonarr_id=None, tvdb_id=None, year=None
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,355 - pipeline_02 - INFO - 🔍 Step 1: Getting Sonarr series and queue...
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,358 - pipeline_02 - INFO -    Retrieved 2 series from Sonarr
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,360 - pipeline_02 - INFO -    Retrieved 10 queue items from Sonarr
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,360 - pipeline_02 - INFO - 🎯 Step 2: Finding TV series using multiple matching strategies...
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,360 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,360 - pipeline_02 - INFO -    Found 7 additional queue items by title matching
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,360 - pipeline_02 - INFO -    Removing 7 queue items...
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,377 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S02E21.1080p.BluRay.x264-TAXES (ID: *********)
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,390 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S01E47.720p.BluRay.x264-TAXES (ID: 2122406966)
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,406 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S02E13.1080p.BluRay.x264-TAXES (ID: 1339259850)
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,421 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S02E19.1080p.BluRay.x264-TAXES (ID: 1078779791)
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,435 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S01E48.720p.BluRay.x264-TAXES (ID: *********)
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,453 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S02E11.1080p.BluRay.x264-TAXES (ID: 2088088014)
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,499 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S02E05.1080p.BluRay.x264-TAXES (ID: *********)
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,499 - pipeline_02 - INFO -    Series not found in Sonarr library - cleanup complete
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,500 - pipeline_02 - INFO - 🎉 Enhanced Sonarr cleanup completed successfully for: Steven Universe
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,500 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E12.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,500 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E12.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,500 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,500 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E12.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,500 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E12.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,501 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,501 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): name 'detect_content_type_from_name' is not defined
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,501 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S02E12.1080p.BluRay.x264-TAXES'
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,505 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,505 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,505 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,506 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,506 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,544 - pipeline_02 - INFO - Detected resolution 1920x1080 for Steven.Universe.S02E12.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,544 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 14:45:19] [STDERR] [+0:00:10] 2025-09-18 14:45:19,544 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,379 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,380 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S02E12.1080p.BluRay.x264-TAXES\Steven.Universe.S02E12.1080p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Steven Universe (2013)\Season 02\S02E12.mkv'
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,380 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,381 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S02E12.1080p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,382 - pipeline_02 - INFO - 📝 Updated .organized - S02E12 with metadata
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,382 - pipeline_02 - INFO - ✅ Episode-aware marker set for S02E12
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,383 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E12.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,383 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S02E12.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,385 - pipeline_02 - INFO -    Checking 9 queue items for pending episodes...
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,385 - pipeline_02 - INFO -    No pending episodes found - safe to cleanup
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,386 - pipeline_02 - INFO -      🧹 All episodes complete - running Sonarr cleanup
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,386 - pipeline_02 - INFO - 🧹 Starting enhanced Sonarr cleanup for: Steven Universe
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,386 - pipeline_02 - INFO -    Available IDs: sonarr_id=None, tvdb_id=None, year=None
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,386 - pipeline_02 - INFO - 🔍 Step 1: Getting Sonarr series and queue...
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,389 - pipeline_02 - INFO -    Retrieved 2 series from Sonarr
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,391 - pipeline_02 - INFO -    Retrieved 9 queue items from Sonarr
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,391 - pipeline_02 - INFO - 🎯 Step 2: Finding TV series using multiple matching strategies...
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,391 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,391 - pipeline_02 - INFO -    Found 3 additional queue items by title matching
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,391 - pipeline_02 - INFO -    Removing 3 queue items...
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,416 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S02E18.1080p.BluRay.x264-TAXES (ID: 1207492774)
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,429 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S02E12.1080p.BluRay.x264-TAXES (ID: 1531688346)
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,447 - pipeline_02 - INFO -    ✅ Removed queue item: Steven.Universe.S02E04.1080p.BluRay.x264-TAXES (ID: *********)
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,447 - pipeline_02 - INFO -    Series not found in Sonarr library - cleanup complete
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,448 - pipeline_02 - INFO - 🎉 Enhanced Sonarr cleanup completed successfully for: Steven Universe
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,448 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E13.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,448 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E13.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,448 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,448 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E13.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,449 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E13.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,449 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,449 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): name 'detect_content_type_from_name' is not defined
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,449 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S02E13.1080p.BluRay.x264-TAXES'
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,452 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,452 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,452 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,452 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,453 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,485 - pipeline_02 - INFO - Detected resolution 1920x1080 for Steven.Universe.S02E13.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,485 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 14:45:20] [STDERR] [+0:00:11] 2025-09-18 14:45:20,485 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,559 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,560 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S02E13.1080p.BluRay.x264-TAXES\Steven.Universe.S02E13.1080p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Steven Universe (2013)\Season 02\S02E13.mkv'
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,560 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,560 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S02E13.1080p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,561 - pipeline_02 - INFO - 📝 Updated .organized - S02E13 with metadata
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,561 - pipeline_02 - INFO - ✅ Episode-aware marker set for S02E13
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,562 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E13.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,562 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S02E13.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,565 - pipeline_02 - INFO -    Checking 6 queue items for pending episodes...
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,565 - pipeline_02 - INFO -    No pending episodes found - safe to cleanup
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,565 - pipeline_02 - INFO -      🧹 All episodes complete - running Sonarr cleanup
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,566 - pipeline_02 - INFO - 🧹 Starting enhanced Sonarr cleanup for: Steven Universe
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,566 - pipeline_02 - INFO -    Available IDs: sonarr_id=None, tvdb_id=None, year=None
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,566 - pipeline_02 - INFO - 🔍 Step 1: Getting Sonarr series and queue...
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,570 - pipeline_02 - INFO -    Retrieved 2 series from Sonarr
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,571 - pipeline_02 - INFO -    Retrieved 6 queue items from Sonarr
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,572 - pipeline_02 - INFO - 🎯 Step 2: Finding TV series using multiple matching strategies...
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,572 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,572 - pipeline_02 - INFO -    Found 0 additional queue items by title matching
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,572 - pipeline_02 - INFO -    No queue items found to remove
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,572 - pipeline_02 - INFO -    Series not found in Sonarr library - cleanup complete
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,572 - pipeline_02 - INFO - 🎉 Enhanced Sonarr cleanup completed successfully for: Steven Universe
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,573 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E14.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,573 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E14.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,573 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,573 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E14.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,573 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E14.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,573 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,573 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): name 'detect_content_type_from_name' is not defined
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,573 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S02E14.1080p.BluRay.x264-TAXES'
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,578 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,578 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,578 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,578 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,578 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,605 - pipeline_02 - INFO - Detected resolution 1920x1080 for Steven.Universe.S02E14.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,605 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 14:45:21] [STDERR] [+0:00:12] 2025-09-18 14:45:21,605 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,587 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,588 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S02E14.1080p.BluRay.x264-TAXES\Steven.Universe.S02E14.1080p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Steven Universe (2013)\Season 02\S02E14.mkv'
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,588 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,589 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S02E14.1080p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,590 - pipeline_02 - INFO - 📝 Updated .organized - S02E14 with metadata
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,590 - pipeline_02 - INFO - ✅ Episode-aware marker set for S02E14
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,590 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E14.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,591 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S02E14.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,593 - pipeline_02 - INFO -    Checking 6 queue items for pending episodes...
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,593 - pipeline_02 - INFO -    No pending episodes found - safe to cleanup
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,593 - pipeline_02 - INFO -      🧹 All episodes complete - running Sonarr cleanup
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,593 - pipeline_02 - INFO - 🧹 Starting enhanced Sonarr cleanup for: Steven Universe
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,594 - pipeline_02 - INFO -    Available IDs: sonarr_id=None, tvdb_id=None, year=None
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,594 - pipeline_02 - INFO - 🔍 Step 1: Getting Sonarr series and queue...
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,597 - pipeline_02 - INFO -    Retrieved 2 series from Sonarr
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,598 - pipeline_02 - INFO -    Retrieved 6 queue items from Sonarr
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,598 - pipeline_02 - INFO - 🎯 Step 2: Finding TV series using multiple matching strategies...
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,598 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,598 - pipeline_02 - INFO -    Found 0 additional queue items by title matching
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,598 - pipeline_02 - INFO -    No queue items found to remove
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,598 - pipeline_02 - INFO -    Series not found in Sonarr library - cleanup complete
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,599 - pipeline_02 - INFO - 🎉 Enhanced Sonarr cleanup completed successfully for: Steven Universe
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,599 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E15.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,599 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E15.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,599 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,599 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E15.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,599 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E15.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,599 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,600 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): name 'detect_content_type_from_name' is not defined
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,600 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S02E15.1080p.BluRay.x264-TAXES'
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,603 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,603 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,603 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,604 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,604 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,630 - pipeline_02 - INFO - Detected resolution 1920x1080 for Steven.Universe.S02E15.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,630 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 14:45:22] [STDERR] [+0:00:13] 2025-09-18 14:45:22,630 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,307 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,309 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S02E15.1080p.BluRay.x264-TAXES\Steven.Universe.S02E15.1080p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Steven Universe (2013)\Season 02\S02E15.mkv'
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,309 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,309 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S02E15.1080p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,310 - pipeline_02 - INFO - 📝 Updated .organized - S02E15 with metadata
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,310 - pipeline_02 - INFO - ✅ Episode-aware marker set for S02E15
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,311 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E15.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,311 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S02E15.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,313 - pipeline_02 - INFO -    Checking 6 queue items for pending episodes...
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,313 - pipeline_02 - INFO -    No pending episodes found - safe to cleanup
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,313 - pipeline_02 - INFO -      🧹 All episodes complete - running Sonarr cleanup
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,314 - pipeline_02 - INFO - 🧹 Starting enhanced Sonarr cleanup for: Steven Universe
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,314 - pipeline_02 - INFO -    Available IDs: sonarr_id=None, tvdb_id=None, year=None
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,314 - pipeline_02 - INFO - 🔍 Step 1: Getting Sonarr series and queue...
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,317 - pipeline_02 - INFO -    Retrieved 2 series from Sonarr
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,318 - pipeline_02 - INFO -    Retrieved 6 queue items from Sonarr
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,318 - pipeline_02 - INFO - 🎯 Step 2: Finding TV series using multiple matching strategies...
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,319 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,319 - pipeline_02 - INFO -    Found 0 additional queue items by title matching
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,319 - pipeline_02 - INFO -    No queue items found to remove
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,319 - pipeline_02 - INFO -    Series not found in Sonarr library - cleanup complete
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,319 - pipeline_02 - INFO - 🎉 Enhanced Sonarr cleanup completed successfully for: Steven Universe
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,319 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E16.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,320 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E16.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,320 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,320 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E16.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,320 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E16.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,320 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,320 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): name 'detect_content_type_from_name' is not defined
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,320 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S02E16.1080p.BluRay.x264-TAXES'
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,323 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,323 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,323 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,323 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,323 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,351 - pipeline_02 - INFO - Detected resolution 1920x1080 for Steven.Universe.S02E16.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,351 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 14:45:24] [STDERR] [+0:00:15] 2025-09-18 14:45:24,351 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,012 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,013 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S02E16.1080p.BluRay.x264-TAXES\Steven.Universe.S02E16.1080p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Steven Universe (2013)\Season 02\S02E16.mkv'
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,013 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,014 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S02E16.1080p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,014 - pipeline_02 - INFO - 📝 Updated .organized - S02E16 with metadata
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,015 - pipeline_02 - INFO - ✅ Episode-aware marker set for S02E16
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,015 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E16.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,015 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S02E16.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,018 - pipeline_02 - INFO -    Checking 6 queue items for pending episodes...
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,018 - pipeline_02 - INFO -    No pending episodes found - safe to cleanup
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,018 - pipeline_02 - INFO -      🧹 All episodes complete - running Sonarr cleanup
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,018 - pipeline_02 - INFO - 🧹 Starting enhanced Sonarr cleanup for: Steven Universe
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,018 - pipeline_02 - INFO -    Available IDs: sonarr_id=None, tvdb_id=None, year=None
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,018 - pipeline_02 - INFO - 🔍 Step 1: Getting Sonarr series and queue...
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,023 - pipeline_02 - INFO -    Retrieved 2 series from Sonarr
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,024 - pipeline_02 - INFO -    Retrieved 6 queue items from Sonarr
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,025 - pipeline_02 - INFO - 🎯 Step 2: Finding TV series using multiple matching strategies...
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,025 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,025 - pipeline_02 - INFO -    Found 0 additional queue items by title matching
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,025 - pipeline_02 - INFO -    No queue items found to remove
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,025 - pipeline_02 - INFO -    Series not found in Sonarr library - cleanup complete
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,025 - pipeline_02 - INFO - 🎉 Enhanced Sonarr cleanup completed successfully for: Steven Universe
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,025 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E17.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,025 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E17.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,026 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,026 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E17.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,026 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E17.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,026 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,026 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): name 'detect_content_type_from_name' is not defined
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,026 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S02E17.1080p.BluRay.x264-TAXES'
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,029 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,029 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,029 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,029 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,029 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,058 - pipeline_02 - INFO - Detected resolution 1920x1080 for Steven.Universe.S02E17.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,058 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 14:45:26] [STDERR] [+0:00:17] 2025-09-18 14:45:26,058 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,085 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,086 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S02E17.1080p.BluRay.x264-TAXES\Steven.Universe.S02E17.1080p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Steven Universe (2013)\Season 02\S02E17.mkv'
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,086 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,087 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S02E17.1080p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,087 - pipeline_02 - INFO - 📝 Updated .organized - S02E17 with metadata
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,087 - pipeline_02 - INFO - ✅ Episode-aware marker set for S02E17
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,088 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E17.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,088 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S02E17.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,090 - pipeline_02 - INFO -    Checking 6 queue items for pending episodes...
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,091 - pipeline_02 - INFO -    No pending episodes found - safe to cleanup
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,091 - pipeline_02 - INFO -      🧹 All episodes complete - running Sonarr cleanup
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,091 - pipeline_02 - INFO - 🧹 Starting enhanced Sonarr cleanup for: Steven Universe
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,091 - pipeline_02 - INFO -    Available IDs: sonarr_id=None, tvdb_id=None, year=None
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,091 - pipeline_02 - INFO - 🔍 Step 1: Getting Sonarr series and queue...
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,095 - pipeline_02 - INFO -    Retrieved 2 series from Sonarr
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,097 - pipeline_02 - INFO -    Retrieved 6 queue items from Sonarr
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,097 - pipeline_02 - INFO - 🎯 Step 2: Finding TV series using multiple matching strategies...
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,097 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,097 - pipeline_02 - INFO -    Found 0 additional queue items by title matching
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,097 - pipeline_02 - INFO -    No queue items found to remove
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,097 - pipeline_02 - INFO -    Series not found in Sonarr library - cleanup complete
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,097 - pipeline_02 - INFO - 🎉 Enhanced Sonarr cleanup completed successfully for: Steven Universe
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,098 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E18.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,098 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E18.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,098 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,098 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E18.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,099 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E18.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,099 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,099 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): name 'detect_content_type_from_name' is not defined
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,099 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S02E18.1080p.BluRay.x264-TAXES'
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,104 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,104 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,104 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,104 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,104 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,131 - pipeline_02 - INFO - Detected resolution 1920x1080 for Steven.Universe.S02E18.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,131 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 14:45:27] [STDERR] [+0:00:18] 2025-09-18 14:45:27,131 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,415 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,416 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S02E18.1080p.BluRay.x264-TAXES\Steven.Universe.S02E18.1080p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Steven Universe (2013)\Season 02\S02E18.mkv'
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,416 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,417 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S02E18.1080p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,417 - pipeline_02 - INFO - 📝 Updated .organized - S02E18 with metadata
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,417 - pipeline_02 - INFO - ✅ Episode-aware marker set for S02E18
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,418 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E18.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,418 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S02E18.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,420 - pipeline_02 - INFO -    Checking 6 queue items for pending episodes...
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,420 - pipeline_02 - INFO -    No pending episodes found - safe to cleanup
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,421 - pipeline_02 - INFO -      🧹 All episodes complete - running Sonarr cleanup
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,421 - pipeline_02 - INFO - 🧹 Starting enhanced Sonarr cleanup for: Steven Universe
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,421 - pipeline_02 - INFO -    Available IDs: sonarr_id=None, tvdb_id=None, year=None
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,421 - pipeline_02 - INFO - 🔍 Step 1: Getting Sonarr series and queue...
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,424 - pipeline_02 - INFO -    Retrieved 2 series from Sonarr
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,425 - pipeline_02 - INFO -    Retrieved 6 queue items from Sonarr
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,425 - pipeline_02 - INFO - 🎯 Step 2: Finding TV series using multiple matching strategies...
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,426 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,426 - pipeline_02 - INFO -    Found 0 additional queue items by title matching
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,426 - pipeline_02 - INFO -    No queue items found to remove
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,426 - pipeline_02 - INFO -    Series not found in Sonarr library - cleanup complete
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,426 - pipeline_02 - INFO - 🎉 Enhanced Sonarr cleanup completed successfully for: Steven Universe
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,427 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E19.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,427 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E19.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,427 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,427 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E19.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,427 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E19.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,427 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,427 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): name 'detect_content_type_from_name' is not defined
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,427 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S02E19.1080p.BluRay.x264-TAXES'
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,431 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,431 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,431 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,431 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,431 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,458 - pipeline_02 - INFO - Detected resolution 1920x1080 for Steven.Universe.S02E19.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,458 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 14:45:28] [STDERR] [+0:00:19] 2025-09-18 14:45:28,458 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,734 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,735 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S02E19.1080p.BluRay.x264-TAXES\Steven.Universe.S02E19.1080p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Steven Universe (2013)\Season 02\S02E19.mkv'
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,735 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,736 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S02E19.1080p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,736 - pipeline_02 - INFO - 📝 Updated .organized - S02E19 with metadata
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,736 - pipeline_02 - INFO - ✅ Episode-aware marker set for S02E19
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,737 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E19.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,737 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S02E19.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,740 - pipeline_02 - INFO -    Checking 6 queue items for pending episodes...
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,740 - pipeline_02 - INFO -    No pending episodes found - safe to cleanup
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,740 - pipeline_02 - INFO -      🧹 All episodes complete - running Sonarr cleanup
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,740 - pipeline_02 - INFO - 🧹 Starting enhanced Sonarr cleanup for: Steven Universe
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,740 - pipeline_02 - INFO -    Available IDs: sonarr_id=None, tvdb_id=None, year=None
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,740 - pipeline_02 - INFO - 🔍 Step 1: Getting Sonarr series and queue...
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,743 - pipeline_02 - INFO -    Retrieved 2 series from Sonarr
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,744 - pipeline_02 - INFO -    Retrieved 6 queue items from Sonarr
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,745 - pipeline_02 - INFO - 🎯 Step 2: Finding TV series using multiple matching strategies...
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,745 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,745 - pipeline_02 - INFO -    Found 0 additional queue items by title matching
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,745 - pipeline_02 - INFO -    No queue items found to remove
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,745 - pipeline_02 - INFO -    Series not found in Sonarr library - cleanup complete
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,745 - pipeline_02 - INFO - 🎉 Enhanced Sonarr cleanup completed successfully for: Steven Universe
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,746 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E20.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,746 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E20.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,746 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,746 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E20.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,746 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E20.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,746 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,746 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): name 'detect_content_type_from_name' is not defined
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,746 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S02E20.1080p.BluRay.x264-TAXES'
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,750 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,750 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,750 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,750 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,750 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,778 - pipeline_02 - INFO - Detected resolution 1920x1080 for Steven.Universe.S02E20.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,778 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 14:45:29] [STDERR] [+0:00:20] 2025-09-18 14:45:29,778 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,174 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,176 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S02E20.1080p.BluRay.x264-TAXES\Steven.Universe.S02E20.1080p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Steven Universe (2013)\Season 02\S02E20.mkv'
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,176 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,176 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S02E20.1080p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,177 - pipeline_02 - INFO - 📝 Updated .organized - S02E20 with metadata
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,177 - pipeline_02 - INFO - ✅ Episode-aware marker set for S02E20
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,178 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E20.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,178 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S02E20.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,181 - pipeline_02 - INFO -    Checking 6 queue items for pending episodes...
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,181 - pipeline_02 - INFO -    No pending episodes found - safe to cleanup
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,181 - pipeline_02 - INFO -      🧹 All episodes complete - running Sonarr cleanup
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,181 - pipeline_02 - INFO - 🧹 Starting enhanced Sonarr cleanup for: Steven Universe
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,182 - pipeline_02 - INFO -    Available IDs: sonarr_id=None, tvdb_id=None, year=None
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,182 - pipeline_02 - INFO - 🔍 Step 1: Getting Sonarr series and queue...
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,187 - pipeline_02 - INFO -    Retrieved 2 series from Sonarr
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,189 - pipeline_02 - INFO -    Retrieved 6 queue items from Sonarr
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,189 - pipeline_02 - INFO - 🎯 Step 2: Finding TV series using multiple matching strategies...
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,189 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,189 - pipeline_02 - INFO -    Found 0 additional queue items by title matching
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,189 - pipeline_02 - INFO -    No queue items found to remove
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,189 - pipeline_02 - INFO -    Series not found in Sonarr library - cleanup complete
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,189 - pipeline_02 - INFO - 🎉 Enhanced Sonarr cleanup completed successfully for: Steven Universe
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,190 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E21.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,190 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E21.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,190 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,190 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E21.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,190 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E21.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,190 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,190 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): name 'detect_content_type_from_name' is not defined
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,190 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S02E21.1080p.BluRay.x264-TAXES'
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,195 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,195 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,195 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,195 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,195 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,223 - pipeline_02 - INFO - Detected resolution 1920x1080 for Steven.Universe.S02E21.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,223 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 14:45:31] [STDERR] [+0:00:22] 2025-09-18 14:45:31,223 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,042 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,043 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S02E21.1080p.BluRay.x264-TAXES\Steven.Universe.S02E21.1080p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Steven Universe (2013)\Season 02\S02E21.mkv'
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,043 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,044 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S02E21.1080p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,044 - pipeline_02 - INFO - 📝 Updated .organized - S02E21 with metadata
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,045 - pipeline_02 - INFO - ✅ Episode-aware marker set for S02E21
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,045 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E21.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,046 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S02E21.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,048 - pipeline_02 - INFO -    Checking 6 queue items for pending episodes...
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,048 - pipeline_02 - INFO -    No pending episodes found - safe to cleanup
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,048 - pipeline_02 - INFO -      🧹 All episodes complete - running Sonarr cleanup
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,048 - pipeline_02 - INFO - 🧹 Starting enhanced Sonarr cleanup for: Steven Universe
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,048 - pipeline_02 - INFO -    Available IDs: sonarr_id=None, tvdb_id=None, year=None
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,049 - pipeline_02 - INFO - 🔍 Step 1: Getting Sonarr series and queue...
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,051 - pipeline_02 - INFO -    Retrieved 2 series from Sonarr
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,053 - pipeline_02 - INFO -    Retrieved 6 queue items from Sonarr
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,053 - pipeline_02 - INFO - 🎯 Step 2: Finding TV series using multiple matching strategies...
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,053 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,053 - pipeline_02 - INFO -    Found 0 additional queue items by title matching
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,053 - pipeline_02 - INFO -    No queue items found to remove
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,053 - pipeline_02 - INFO -    Series not found in Sonarr library - cleanup complete
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,053 - pipeline_02 - INFO - 🎉 Enhanced Sonarr cleanup completed successfully for: Steven Universe
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,054 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E22.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,054 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E22.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,054 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,054 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E22.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,054 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E22.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,054 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,054 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): name 'detect_content_type_from_name' is not defined
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,054 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S02E22.1080p.BluRay.x264-TAXES'
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,058 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,058 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,058 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,058 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,058 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,086 - pipeline_02 - INFO - Detected resolution 1920x1080 for Steven.Universe.S02E22.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,086 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 14:45:33] [STDERR] [+0:00:24] 2025-09-18 14:45:33,086 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,697 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,699 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S02E22.1080p.BluRay.x264-TAXES\Steven.Universe.S02E22.1080p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Steven Universe (2013)\Season 02\S02E22.mkv'
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,699 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,699 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S02E22.1080p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,700 - pipeline_02 - INFO - 📝 Updated .organized - S02E22 with metadata
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,700 - pipeline_02 - INFO - ✅ Episode-aware marker set for S02E22
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,701 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E22.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,701 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S02E22.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,703 - pipeline_02 - INFO -    Checking 6 queue items for pending episodes...
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,703 - pipeline_02 - INFO -    No pending episodes found - safe to cleanup
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,704 - pipeline_02 - INFO -      🧹 All episodes complete - running Sonarr cleanup
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,704 - pipeline_02 - INFO - 🧹 Starting enhanced Sonarr cleanup for: Steven Universe
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,704 - pipeline_02 - INFO -    Available IDs: sonarr_id=None, tvdb_id=None, year=None
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,704 - pipeline_02 - INFO - 🔍 Step 1: Getting Sonarr series and queue...
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,707 - pipeline_02 - INFO -    Retrieved 2 series from Sonarr
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,708 - pipeline_02 - INFO -    Retrieved 6 queue items from Sonarr
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,708 - pipeline_02 - INFO - 🎯 Step 2: Finding TV series using multiple matching strategies...
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,708 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,708 - pipeline_02 - INFO -    Found 0 additional queue items by title matching
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,708 - pipeline_02 - INFO -    No queue items found to remove
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,708 - pipeline_02 - INFO -    Series not found in Sonarr library - cleanup complete
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,709 - pipeline_02 - INFO - 🎉 Enhanced Sonarr cleanup completed successfully for: Steven Universe
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,709 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E23.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,709 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E23.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,709 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,709 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E23.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,709 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E23.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,709 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,710 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): name 'detect_content_type_from_name' is not defined
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,710 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S02E23.1080p.BluRay.x264-TAXES'
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,714 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,714 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,714 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,714 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,714 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,741 - pipeline_02 - INFO - Detected resolution 1920x1080 for Steven.Universe.S02E23.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,741 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 14:45:34] [STDERR] [+0:00:25] 2025-09-18 14:45:34,741 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,173 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,174 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S02E23.1080p.BluRay.x264-TAXES\Steven.Universe.S02E23.1080p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Steven Universe (2013)\Season 02\S02E23.mkv'
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,175 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,175 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S02E23.1080p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,176 - pipeline_02 - INFO - 📝 Updated .organized - S02E23 with metadata
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,176 - pipeline_02 - INFO - ✅ Episode-aware marker set for S02E23
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,177 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E23.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,177 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S02E23.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,179 - pipeline_02 - INFO -    Checking 6 queue items for pending episodes...
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,180 - pipeline_02 - INFO -    No pending episodes found - safe to cleanup
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,180 - pipeline_02 - INFO -      🧹 All episodes complete - running Sonarr cleanup
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,180 - pipeline_02 - INFO - 🧹 Starting enhanced Sonarr cleanup for: Steven Universe
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,180 - pipeline_02 - INFO -    Available IDs: sonarr_id=None, tvdb_id=None, year=None
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,180 - pipeline_02 - INFO - 🔍 Step 1: Getting Sonarr series and queue...
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,183 - pipeline_02 - INFO -    Retrieved 2 series from Sonarr
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,184 - pipeline_02 - INFO -    Retrieved 6 queue items from Sonarr
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,185 - pipeline_02 - INFO - 🎯 Step 2: Finding TV series using multiple matching strategies...
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,185 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,185 - pipeline_02 - INFO -    Found 0 additional queue items by title matching
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,185 - pipeline_02 - INFO -    No queue items found to remove
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,185 - pipeline_02 - INFO -    Series not found in Sonarr library - cleanup complete
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,185 - pipeline_02 - INFO - 🎉 Enhanced Sonarr cleanup completed successfully for: Steven Universe
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,185 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E24.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,185 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E24.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,186 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,186 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E24.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,186 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E24.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,186 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,186 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): name 'detect_content_type_from_name' is not defined
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,186 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S02E24.1080p.BluRay.x264-TAXES'
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,189 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,189 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,189 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,189 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,190 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,218 - pipeline_02 - INFO - Detected resolution 1920x1080 for Steven.Universe.S02E24.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,218 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 14:45:37] [STDERR] [+0:00:28] 2025-09-18 14:45:37,218 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,590 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,591 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S02E24.1080p.BluRay.x264-TAXES\Steven.Universe.S02E24.1080p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Steven Universe (2013)\Season 02\S02E24.mkv'
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,591 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,591 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S02E24.1080p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,592 - pipeline_02 - INFO - 📝 Updated .organized - S02E24 with metadata
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,592 - pipeline_02 - INFO - ✅ Episode-aware marker set for S02E24
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,593 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E24.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,593 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S02E24.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,596 - pipeline_02 - INFO -    Checking 6 queue items for pending episodes...
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,596 - pipeline_02 - INFO -    No pending episodes found - safe to cleanup
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,596 - pipeline_02 - INFO -      🧹 All episodes complete - running Sonarr cleanup
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,596 - pipeline_02 - INFO - 🧹 Starting enhanced Sonarr cleanup for: Steven Universe
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,597 - pipeline_02 - INFO -    Available IDs: sonarr_id=None, tvdb_id=None, year=None
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,597 - pipeline_02 - INFO - 🔍 Step 1: Getting Sonarr series and queue...
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,601 - pipeline_02 - INFO -    Retrieved 2 series from Sonarr
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,603 - pipeline_02 - INFO -    Retrieved 6 queue items from Sonarr
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,603 - pipeline_02 - INFO - 🎯 Step 2: Finding TV series using multiple matching strategies...
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,604 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,604 - pipeline_02 - INFO -    Found 0 additional queue items by title matching
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,604 - pipeline_02 - INFO -    No queue items found to remove
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,604 - pipeline_02 - INFO -    Series not found in Sonarr library - cleanup complete
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,604 - pipeline_02 - INFO - 🎉 Enhanced Sonarr cleanup completed successfully for: Steven Universe
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,604 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E25.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,604 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E25.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,605 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,605 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E25.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,605 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E25.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,605 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,605 - pipeline_02 - ERROR - Content-type validation gate error (non-fatal): name 'detect_content_type_from_name' is not defined
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,605 - pipeline_02 - WARNING - 🚨 Database has messy title: 'Steven.Universe.S02E25.1080p.BluRay.x264-TAXES'
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,610 - pipeline_02 - INFO - ✅ Fixed with clean API title: 'Steven Universe'
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,610 - pipeline_02 - INFO - 📡 Using official title 'Steven Universe' from Radarr
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,610 - pipeline_02 - INFO - 📝 Enhanced content_info: title='Steven Universe', year='N/A', type='tv_show'
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,610 - pipeline_02 - INFO - 🎯 Priority: Radarr metadata takes precedence over folder extraction
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,610 - pipeline_02 - INFO - 🎥 Analyzing resolution with ffprobe (most accurate)...
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,652 - pipeline_02 - INFO - Detected resolution 1920x1080 for Steven.Universe.S02E25.1080p.BluRay.x264-TAXES.mkv
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,652 - pipeline_02 - INFO - 🎥 ffprobe detection: 1080p (1920x1080) from video file analysis
[2025-09-18 14:45:38] [STDERR] [+0:00:29] 2025-09-18 14:45:38,652 - pipeline_02 - INFO - 📊 Final resolution determination: 1080p (width: 1920px)
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,923 - pipeline_02 - INFO - 📡 Retrieved year from Sonarr: 2013
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,924 - pipeline_02 - INFO - Moved: 'workspace\1_downloading\complete_raw\Steven.Universe.S02E25.1080p.BluRay.x264-TAXES\Steven.Universe.S02E25.1080p.BluRay.x264-TAXES.mkv' to 'workspace\2_downloaded_and_organized\tv_shows\1080p\Steven Universe (2013)\Season 02\S02E25.mkv'
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,924 - pipeline_02 - INFO - Successfully organized TV show file. Cleaning up raw download folder.
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,925 - _internal.utils.common_helpers - INFO - Deleted folder: workspace\1_downloading\complete_raw\Steven.Universe.S02E25.1080p.BluRay.x264-TAXES (ignore_errors=True)
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,925 - pipeline_02 - INFO - 📝 Updated .organized - S02E25 with metadata
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,925 - pipeline_02 - INFO - ✅ Episode-aware marker set for S02E25
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,926 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E25.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,926 - pipeline_02 - INFO -      ✅ Successfully organized TV show: Steven.Universe.S02E25.1080p.BluRay.x264-TAXES
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,928 - pipeline_02 - INFO -    Checking 6 queue items for pending episodes...
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,928 - pipeline_02 - INFO -    No pending episodes found - safe to cleanup
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,929 - pipeline_02 - INFO -      🧹 All episodes complete - running Sonarr cleanup
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,929 - pipeline_02 - INFO - 🧹 Starting enhanced Sonarr cleanup for: Steven Universe
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,929 - pipeline_02 - INFO -    Available IDs: sonarr_id=None, tvdb_id=None, year=None
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,929 - pipeline_02 - INFO - 🔍 Step 1: Getting Sonarr series and queue...
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,932 - pipeline_02 - INFO -    Retrieved 2 series from Sonarr
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,933 - pipeline_02 - INFO -    Retrieved 6 queue items from Sonarr
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,933 - pipeline_02 - INFO - 🎯 Step 2: Finding TV series using multiple matching strategies...
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,933 - pipeline_02 - INFO - 🗑️ Step 3: Cleaning up queue items...
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,933 - pipeline_02 - INFO -    Found 0 additional queue items by title matching
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,933 - pipeline_02 - INFO -    No queue items found to remove
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,933 - pipeline_02 - INFO -    Series not found in Sonarr library - cleanup complete
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,934 - pipeline_02 - INFO - 🎉 Enhanced Sonarr cleanup completed successfully for: Steven Universe
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,934 - pipeline_02 - INFO - 🔄 Checking for dynamic season progression opportunities...
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,934 - pipeline_02 - INFO -      Sequential progression enabled for 0 series
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,934 - pipeline_02 - INFO -      No series opted-in for sequential progression
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,934 - pipeline_02 - INFO - ===== Finished Modern Sonarr TV Show Download Monitoring =====
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,934 - pipeline_02 - INFO -     ✅ Pipeline 02 completed successfully (TV Shows)
[2025-09-18 14:45:39] [STDERR] [+0:00:31] 2025-09-18 14:45:39,934 - pipeline_02 - INFO - ===== Finished Pipeline 02 Execution =====
[2025-09-18 14:45:39] [STDOUT] [+0:00:31] ------------------------------------------------------------
[2025-09-18 14:45:39] [STDOUT] [+0:00:31] 
[2025-09-18 14:45:39] [STDOUT] [+0:00:31] 🏁 Terminal logging ended for 02_download_and_organize
[2025-09-18 14:45:39] [STDOUT] [+0:00:31] 
[2025-09-18 14:45:39] [STDOUT] [+0:00:31] 🕐 Ended at: 2025-09-18 14:45:39
[2025-09-18 14:45:39] [STDOUT] [+0:00:31] 
[2025-09-18 14:45:39] [STDOUT] [+0:00:31] ⏱️ Total duration: 0:00:31.041450
[2025-09-18 14:45:39] [STDOUT] [+0:00:31] 
[2025-09-18 14:45:39] [STDOUT] [+0:00:31] 📄 Log saved to: C:\Users\<USER>\Videos\PlexAutomator\logs\02_download_and_organize_2025-09-18_02-45-08-PM.txt
[2025-09-18 14:45:39] [STDOUT] [+0:00:31] 


==================================================
=== TERMINAL OUTPUT LOG END ===
Script: 02_download_and_organize
Ended: 2025-09-18 14:45:39
Duration: 0:00:31.041450
==================================================
