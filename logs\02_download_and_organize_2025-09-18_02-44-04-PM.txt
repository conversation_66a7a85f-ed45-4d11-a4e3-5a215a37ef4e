=== TERMINAL OUTPUT LOG ===
Script: 02_download_and_organize
Started: 2025-09-18 14:44:04
Log File: C:\Users\<USER>\Videos\PlexAutomator\logs\02_download_and_organize_2025-09-18_02-44-04-PM.txt
==================================================

[2025-09-18 14:44:04] [STDOUT] [+0:00:00] 📝 Terminal logging started for 02_download_and_organize
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] 📄 Log file: C:\Users\<USER>\Videos\PlexAutomator\logs\02_download_and_organize_2025-09-18_02-44-04-PM.txt
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] 🕐 Started at: 2025-09-18 14:44:04
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] ------------------------------------------------------------
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] *** UNIFIED Stage 02: Download and Organize ***
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] ==================================================
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] + Consolidated from multiple O2 scripts into one unified implementation
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] >> Modern Radarr API integration
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] -- Simplified workflow: Radarr -> SABnzbd -> Plex
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] >> Clean, maintainable codebase
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:04] [STDOUT] [+0:00:00]    Default: Interactive mode (use --movies-only, --tv-only, or --all for command-line mode)
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,694 - pipeline_02 - INFO - ===== Starting Pipeline 02 Execution =====
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,696 - pipeline_02 - INFO - Settings loaded successfully
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,696 - pipeline_02 - INFO - Command-line mode: Processing tv_shows
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,696 - pipeline_02 - INFO - 📺 Starting Sonarr (TV Shows) monitoring...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,696 - pipeline_02 - INFO - ===== Starting Modern Sonarr TV Show Download Monitoring with SQLite =====
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,696 - pipeline_02 - INFO -      ENHANCED: Dual-detection system (Filesystem + Sonarr API) + SQLite state
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,698 - _internal.utils.filesystem_first_state_manager - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,698 - pipeline_02 - INFO - Discovering TV shows by scanning filesystem...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,699 - pipeline_02 - INFO - Found 0 content items across 14 stages
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,700 - pipeline_02 - INFO -      SABnzbd complete directory: workspace\1_downloading\complete_raw
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,700 - pipeline_02 - INFO -      Sonarr API endpoint: http://localhost:8989
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,700 - pipeline_02 - INFO -      SMART STATE VALIDATION: Checking for inconsistent TV show states...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,709 - pipeline_02 - INFO - Retrieved 2 TV series from Sonarr
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,711 - pipeline_02 - INFO -      Active TV show downloads in Sonarr queue: 10
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,712 - pipeline_02 - INFO -      ENHANCED: Checking both Sonarr API and filesystem for completed TV shows
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,712 - pipeline_02 - INFO -      Will scan SABnzbd directory: workspace\1_downloading\complete_raw
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,712 - pipeline_02 - INFO - Selected main video file: Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (0.83 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,712 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~0.83 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,713 - pipeline_02 - INFO - Selected main video file: Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (0.88 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,713 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~0.88 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,713 - pipeline_02 - INFO - Selected main video file: Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (0.84 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,713 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~0.84 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,714 - pipeline_02 - INFO - Selected main video file: Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (0.83 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,714 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~0.83 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,714 - pipeline_02 - INFO - Selected main video file: Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (0.94 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,714 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~0.94 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,714 - pipeline_02 - INFO - Selected main video file: Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (0.99 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,714 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~0.99 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,714 - pipeline_02 - INFO - Selected main video file: Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (0.87 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,714 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~0.87 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,715 - pipeline_02 - INFO - Selected main video file: Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (0.94 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,715 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~0.94 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,715 - pipeline_02 - INFO - Selected main video file: Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (1.02 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,715 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~1.02 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,715 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S01E47.720p.BluRay.x264-TAXES.mkv (0.21 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,716 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S01E47.720p.BluRay.x264-TAXES (1 video files, ~0.21 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,716 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S01E48.720p.BluRay.x264-TAXES.mkv (0.21 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,716 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S01E48.720p.BluRay.x264-TAXES (1 video files, ~0.21 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,716 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E11.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,716 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E11.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,716 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E12.1080p.BluRay.x264-TAXES.mkv (0.55 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,716 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E12.1080p.BluRay.x264-TAXES (1 video files, ~0.55 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,717 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E13.1080p.BluRay.x264-TAXES.mkv (0.55 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,717 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E13.1080p.BluRay.x264-TAXES (1 video files, ~0.55 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,717 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E14.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,717 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E14.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,717 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E15.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,717 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E15.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,718 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E16.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,718 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E16.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,718 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E17.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,718 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E17.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,718 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E18.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,719 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E18.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,719 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E19.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,719 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E19.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,719 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E20.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,719 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E20.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,719 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E21.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,719 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E21.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,720 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E22.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,720 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E22.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,720 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E23.1080p.BluRay.x264-TAXES.mkv (0.55 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,720 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E23.1080p.BluRay.x264-TAXES (1 video files, ~0.55 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,720 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E24.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,720 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E24.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,720 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E25.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,721 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E25.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,743 - pipeline_02 - INFO -      FILESYSTEM DETECTION: Found 26 completed TV folders ready for organization
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,743 - pipeline_02 - INFO -      Processing 26 completed TV folders
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,745 - pipeline_02 - INFO -      🔒 Acquired lock for Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso, processing...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,745 - pipeline_02 - INFO -      Organizing completed TV folder: Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,745 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,745 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,745 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Futurama', 'year': None, 'original_folder': 'Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso', 'confidence': 0.95}
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,745 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] C:\Users\<USER>\Videos\PlexAutomator\02_download_and_organize.py:3506: DeprecationWarning: datetime.datetime.utcnow() is deprecated and scheduled for removal in a future version. Use timezone-aware objects to represent datetimes in UTC: datetime.datetime.now(datetime.UTC).
[2025-09-18 14:44:04] [STDERR] [+0:00:00]   stamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,747 - pipeline_02 - WARNING - ⚠️  Content-type gate: expected movie but detector says tv_show (conf=0.95); quarantining workspace\1_downloading\complete_raw\Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso -> workspace\issues_hold\misclassified\movie_as_tv_show_20250918_184404
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,747 - pipeline_02 - INFO -      🔓 Released lock for Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,748 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,748 - pipeline_02 - INFO -      🔒 Acquired lock for Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso, processing...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,748 - pipeline_02 - INFO -      Organizing completed TV folder: Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,748 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,748 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,748 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Futurama', 'year': None, 'original_folder': 'Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso', 'confidence': 0.95}
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,748 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,749 - pipeline_02 - WARNING - ⚠️  Content-type gate: expected movie but detector says tv_show (conf=0.95); quarantining workspace\1_downloading\complete_raw\Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso -> workspace\issues_hold\misclassified\movie_as_tv_show_20250918_184404
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,749 - pipeline_02 - INFO -      🔓 Released lock for Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,749 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,750 - pipeline_02 - INFO -      🔒 Acquired lock for Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso, processing...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,750 - pipeline_02 - INFO -      Organizing completed TV folder: Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,750 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,750 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,750 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Futurama', 'year': None, 'original_folder': 'Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso', 'confidence': 0.95}
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,750 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,750 - pipeline_02 - WARNING - ⚠️  Content-type gate: expected movie but detector says tv_show (conf=0.95); quarantining workspace\1_downloading\complete_raw\Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso -> workspace\issues_hold\misclassified\movie_as_tv_show_20250918_184404
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,751 - pipeline_02 - INFO -      🔓 Released lock for Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,751 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,751 - pipeline_02 - INFO -      🔒 Acquired lock for Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso, processing...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,751 - pipeline_02 - INFO -      Organizing completed TV folder: Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,751 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,751 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,751 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Futurama', 'year': None, 'original_folder': 'Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso', 'confidence': 0.95}
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,752 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,752 - pipeline_02 - WARNING - ⚠️  Content-type gate: expected movie but detector says tv_show (conf=0.95); quarantining workspace\1_downloading\complete_raw\Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso -> workspace\issues_hold\misclassified\movie_as_tv_show_20250918_184404
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,752 - pipeline_02 - INFO -      🔓 Released lock for Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,752 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,753 - pipeline_02 - INFO -      🔒 Acquired lock for Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso, processing...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,753 - pipeline_02 - INFO -      Organizing completed TV folder: Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,753 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,753 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,753 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Futurama', 'year': None, 'original_folder': 'Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso', 'confidence': 0.95}
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,754 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,754 - pipeline_02 - WARNING - ⚠️  Content-type gate: expected movie but detector says tv_show (conf=0.95); quarantining workspace\1_downloading\complete_raw\Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso -> workspace\issues_hold\misclassified\movie_as_tv_show_20250918_184404
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,754 - pipeline_02 - INFO -      🔓 Released lock for Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,754 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,755 - pipeline_02 - INFO -      🔒 Acquired lock for Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso, processing...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,755 - pipeline_02 - INFO -      Organizing completed TV folder: Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,755 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,755 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,755 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Futurama', 'year': None, 'original_folder': 'Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso', 'confidence': 0.95}
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,755 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,755 - pipeline_02 - WARNING - ⚠️  Content-type gate: expected movie but detector says tv_show (conf=0.95); quarantining workspace\1_downloading\complete_raw\Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso -> workspace\issues_hold\misclassified\movie_as_tv_show_20250918_184404
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,756 - pipeline_02 - INFO -      🔓 Released lock for Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,756 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,756 - pipeline_02 - INFO -      🔒 Acquired lock for Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso, processing...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,756 - pipeline_02 - INFO -      Organizing completed TV folder: Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,757 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,757 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,757 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Futurama', 'year': None, 'original_folder': 'Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso', 'confidence': 0.95}
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,757 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,757 - pipeline_02 - WARNING - ⚠️  Content-type gate: expected movie but detector says tv_show (conf=0.95); quarantining workspace\1_downloading\complete_raw\Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso -> workspace\issues_hold\misclassified\movie_as_tv_show_20250918_184404
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,758 - pipeline_02 - INFO -      🔓 Released lock for Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,758 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,758 - pipeline_02 - INFO -      🔒 Acquired lock for Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso, processing...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,758 - pipeline_02 - INFO -      Organizing completed TV folder: Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,758 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,758 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,758 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Futurama', 'year': None, 'original_folder': 'Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso', 'confidence': 0.95}
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,758 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,759 - pipeline_02 - WARNING - ⚠️  Content-type gate: expected movie but detector says tv_show (conf=0.95); quarantining workspace\1_downloading\complete_raw\Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso -> workspace\issues_hold\misclassified\movie_as_tv_show_20250918_184404
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,759 - pipeline_02 - INFO -      🔓 Released lock for Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,759 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,760 - pipeline_02 - INFO -      🔒 Acquired lock for Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso, processing...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,760 - pipeline_02 - INFO -      Organizing completed TV folder: Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,760 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,760 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,760 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Futurama', 'year': None, 'original_folder': 'Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso', 'confidence': 0.95}
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,760 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,760 - pipeline_02 - WARNING - ⚠️  Content-type gate: expected movie but detector says tv_show (conf=0.95); quarantining workspace\1_downloading\complete_raw\Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso -> workspace\issues_hold\misclassified\movie_as_tv_show_20250918_184404
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,761 - pipeline_02 - INFO -      🔓 Released lock for Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,761 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,761 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S01E47.720p.BluRay.x264-TAXES, processing...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,761 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S01E47.720p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,762 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,762 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S01E47.720p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,762 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '720p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S01E47.720p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,762 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,762 - pipeline_02 - WARNING - ⚠️  Content-type gate: expected movie but detector says tv_show (conf=0.95); quarantining workspace\1_downloading\complete_raw\Steven.Universe.S01E47.720p.BluRay.x264-TAXES -> workspace\issues_hold\misclassified\movie_as_tv_show_20250918_184404
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,762 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S01E47.720p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,763 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Steven.Universe.S01E47.720p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,763 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S01E48.720p.BluRay.x264-TAXES, processing...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,763 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S01E48.720p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,763 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,763 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S01E48.720p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,763 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '720p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S01E48.720p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,763 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,764 - pipeline_02 - WARNING - ⚠️  Content-type gate: expected movie but detector says tv_show (conf=0.95); quarantining workspace\1_downloading\complete_raw\Steven.Universe.S01E48.720p.BluRay.x264-TAXES -> workspace\issues_hold\misclassified\movie_as_tv_show_20250918_184404
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,764 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S01E48.720p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,764 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Steven.Universe.S01E48.720p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,765 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E11.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,765 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E11.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,765 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,765 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E11.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,765 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E11.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,765 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,765 - pipeline_02 - WARNING - ⚠️  Content-type gate: expected movie but detector says tv_show (conf=0.95); quarantining workspace\1_downloading\complete_raw\Steven.Universe.S02E11.1080p.BluRay.x264-TAXES -> workspace\issues_hold\misclassified\movie_as_tv_show_20250918_184404
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,766 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E11.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,766 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Steven.Universe.S02E11.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,766 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E12.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,766 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E12.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,766 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,766 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E12.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,766 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E12.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,766 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,767 - pipeline_02 - WARNING - ⚠️  Content-type gate: expected movie but detector says tv_show (conf=0.95); quarantining workspace\1_downloading\complete_raw\Steven.Universe.S02E12.1080p.BluRay.x264-TAXES -> workspace\issues_hold\misclassified\movie_as_tv_show_20250918_184404
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,767 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E12.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,767 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Steven.Universe.S02E12.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,768 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E13.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,768 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E13.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,768 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,768 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E13.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,768 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E13.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,768 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,768 - pipeline_02 - WARNING - ⚠️  Content-type gate: expected movie but detector says tv_show (conf=0.95); quarantining workspace\1_downloading\complete_raw\Steven.Universe.S02E13.1080p.BluRay.x264-TAXES -> workspace\issues_hold\misclassified\movie_as_tv_show_20250918_184404
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,769 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E13.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,769 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Steven.Universe.S02E13.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,770 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E14.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,770 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E14.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,770 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,770 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E14.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,770 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E14.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,770 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,770 - pipeline_02 - WARNING - ⚠️  Content-type gate: expected movie but detector says tv_show (conf=0.95); quarantining workspace\1_downloading\complete_raw\Steven.Universe.S02E14.1080p.BluRay.x264-TAXES -> workspace\issues_hold\misclassified\movie_as_tv_show_20250918_184404
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,771 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E14.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,771 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Steven.Universe.S02E14.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,772 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E15.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,772 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E15.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,772 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,772 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E15.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,772 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E15.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,772 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,772 - pipeline_02 - WARNING - ⚠️  Content-type gate: expected movie but detector says tv_show (conf=0.95); quarantining workspace\1_downloading\complete_raw\Steven.Universe.S02E15.1080p.BluRay.x264-TAXES -> workspace\issues_hold\misclassified\movie_as_tv_show_20250918_184404
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,773 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E15.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,773 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Steven.Universe.S02E15.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,774 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E16.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,774 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E16.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,774 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,774 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E16.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,774 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E16.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,774 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,775 - pipeline_02 - WARNING - ⚠️  Content-type gate: expected movie but detector says tv_show (conf=0.95); quarantining workspace\1_downloading\complete_raw\Steven.Universe.S02E16.1080p.BluRay.x264-TAXES -> workspace\issues_hold\misclassified\movie_as_tv_show_20250918_184404
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,775 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E16.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,775 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Steven.Universe.S02E16.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,776 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E17.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,776 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E17.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,776 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,776 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E17.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,776 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E17.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,776 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,776 - pipeline_02 - WARNING - ⚠️  Content-type gate: expected movie but detector says tv_show (conf=0.95); quarantining workspace\1_downloading\complete_raw\Steven.Universe.S02E17.1080p.BluRay.x264-TAXES -> workspace\issues_hold\misclassified\movie_as_tv_show_20250918_184404
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,777 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E17.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,777 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Steven.Universe.S02E17.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,778 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E18.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,778 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E18.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,778 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,778 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E18.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,778 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E18.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,778 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,778 - pipeline_02 - WARNING - ⚠️  Content-type gate: expected movie but detector says tv_show (conf=0.95); quarantining workspace\1_downloading\complete_raw\Steven.Universe.S02E18.1080p.BluRay.x264-TAXES -> workspace\issues_hold\misclassified\movie_as_tv_show_20250918_184404
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,779 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E18.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,779 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Steven.Universe.S02E18.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,779 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E19.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,779 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E19.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,779 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,779 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E19.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,780 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E19.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,780 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,780 - pipeline_02 - WARNING - ⚠️  Content-type gate: expected movie but detector says tv_show (conf=0.95); quarantining workspace\1_downloading\complete_raw\Steven.Universe.S02E19.1080p.BluRay.x264-TAXES -> workspace\issues_hold\misclassified\movie_as_tv_show_20250918_184404
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,781 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E19.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,781 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Steven.Universe.S02E19.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,781 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E20.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,781 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E20.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,781 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,781 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E20.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,781 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E20.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,781 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,782 - pipeline_02 - WARNING - ⚠️  Content-type gate: expected movie but detector says tv_show (conf=0.95); quarantining workspace\1_downloading\complete_raw\Steven.Universe.S02E20.1080p.BluRay.x264-TAXES -> workspace\issues_hold\misclassified\movie_as_tv_show_20250918_184404
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,782 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E20.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,782 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Steven.Universe.S02E20.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,783 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E21.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,783 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E21.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,783 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,783 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E21.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,783 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E21.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,783 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,783 - pipeline_02 - WARNING - ⚠️  Content-type gate: expected movie but detector says tv_show (conf=0.95); quarantining workspace\1_downloading\complete_raw\Steven.Universe.S02E21.1080p.BluRay.x264-TAXES -> workspace\issues_hold\misclassified\movie_as_tv_show_20250918_184404
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,784 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E21.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,784 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Steven.Universe.S02E21.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,785 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E22.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,785 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E22.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,785 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,785 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E22.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,785 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E22.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,785 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,785 - pipeline_02 - WARNING - ⚠️  Content-type gate: expected movie but detector says tv_show (conf=0.95); quarantining workspace\1_downloading\complete_raw\Steven.Universe.S02E22.1080p.BluRay.x264-TAXES -> workspace\issues_hold\misclassified\movie_as_tv_show_20250918_184404
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,786 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E22.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,786 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Steven.Universe.S02E22.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,786 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E23.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,786 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E23.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,786 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,786 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E23.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,786 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E23.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,786 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,787 - pipeline_02 - WARNING - ⚠️  Content-type gate: expected movie but detector says tv_show (conf=0.95); quarantining workspace\1_downloading\complete_raw\Steven.Universe.S02E23.1080p.BluRay.x264-TAXES -> workspace\issues_hold\misclassified\movie_as_tv_show_20250918_184404
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,787 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E23.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,787 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Steven.Universe.S02E23.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,788 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E24.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,788 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E24.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,788 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,788 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E24.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,788 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E24.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,788 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,788 - pipeline_02 - WARNING - ⚠️  Content-type gate: expected movie but detector says tv_show (conf=0.95); quarantining workspace\1_downloading\complete_raw\Steven.Universe.S02E24.1080p.BluRay.x264-TAXES -> workspace\issues_hold\misclassified\movie_as_tv_show_20250918_184404
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,789 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E24.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,789 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Steven.Universe.S02E24.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,789 - pipeline_02 - INFO -      🔒 Acquired lock for Steven.Universe.S02E25.1080p.BluRay.x264-TAXES, processing...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,789 - pipeline_02 - INFO -      Organizing completed TV folder: Steven.Universe.S02E25.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,789 - pipeline_02 - INFO -      📁 Using Sonarr metadata directly (no year resolver needed)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,789 - pipeline_02 - INFO - 🔍 Analyzing original download folder: Steven.Universe.S02E25.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,789 - pipeline_02 - INFO - 📊 Original folder analysis: {'content_type': 'tv_show', 'resolution': '1080p', 'title': 'Steven Universe', 'year': None, 'original_folder': 'Steven.Universe.S02E25.1080p.BluRay.x264-TAXES', 'confidence': 0.95}
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,789 - pipeline_02 - INFO - 🎯 Content type: tv_show (confidence: 0.95)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,789 - pipeline_02 - WARNING - ⚠️  Content-type gate: expected movie but detector says tv_show (conf=0.95); quarantining workspace\1_downloading\complete_raw\Steven.Universe.S02E25.1080p.BluRay.x264-TAXES -> workspace\issues_hold\misclassified\movie_as_tv_show_20250918_184404
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,790 - pipeline_02 - INFO -      🔓 Released lock for Steven.Universe.S02E25.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,790 - pipeline_02 - ERROR -      ❌ Failed to organize TV show: Steven.Universe.S02E25.1080p.BluRay.x264-TAXES
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,790 - pipeline_02 - INFO - 🔄 Checking for dynamic season progression opportunities...
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,790 - pipeline_02 - INFO -      Sequential progression enabled for 0 series
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,790 - pipeline_02 - INFO -      No series opted-in for sequential progression
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,790 - pipeline_02 - INFO - ===== Finished Modern Sonarr TV Show Download Monitoring =====
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,790 - pipeline_02 - INFO -     ✅ Pipeline 02 completed successfully (TV Shows)
[2025-09-18 14:44:04] [STDERR] [+0:00:00] 2025-09-18 14:44:04,791 - pipeline_02 - INFO - ===== Finished Pipeline 02 Execution =====
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] ------------------------------------------------------------
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] 🏁 Terminal logging ended for 02_download_and_organize
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] 🕐 Ended at: 2025-09-18 14:44:04
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] ⏱️ Total duration: 0:00:00.101681
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] 📄 Log saved to: C:\Users\<USER>\Videos\PlexAutomator\logs\02_download_and_organize_2025-09-18_02-44-04-PM.txt
[2025-09-18 14:44:04] [STDOUT] [+0:00:00] 


==================================================
=== TERMINAL OUTPUT LOG END ===
Script: 02_download_and_organize
Ended: 2025-09-18 14:44:04
Duration: 0:00:00.101681
==================================================
