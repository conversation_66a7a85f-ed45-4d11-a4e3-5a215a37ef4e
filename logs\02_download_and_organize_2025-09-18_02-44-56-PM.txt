=== TERMINAL OUTPUT LOG ===
Script: 02_download_and_organize
Started: 2025-09-18 14:44:56
Log File: C:\Users\<USER>\Videos\PlexAutomator\logs\02_download_and_organize_2025-09-18_02-44-56-PM.txt
==================================================

[2025-09-18 14:44:56] [STDOUT] [+0:00:00] 📝 Terminal logging started for 02_download_and_organize
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] 📄 Log file: C:\Users\<USER>\Videos\PlexAutomator\logs\02_download_and_organize_2025-09-18_02-44-56-PM.txt
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] 🕐 Started at: 2025-09-18 14:44:56
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] ------------------------------------------------------------
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] *** UNIFIED Stage 02: Download and Organize ***
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] ==================================================
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] + Consolidated from multiple O2 scripts into one unified implementation
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] >> Modern Radarr API integration
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] -- Simplified workflow: Radarr -> SABnzbd -> Plex
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] >> Clean, maintainable codebase
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:56] [STDOUT] [+0:00:00]    Default: Interactive mode (use --movies-only, --tv-only, or --all for command-line mode)
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,337 - pipeline_02 - INFO - ===== Starting Pipeline 02 Execution =====
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,339 - pipeline_02 - INFO - Settings loaded successfully
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,339 - pipeline_02 - INFO - Command-line mode: Processing tv_shows
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,339 - pipeline_02 - INFO - 📺 Starting Sonarr (TV Shows) monitoring...
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,339 - pipeline_02 - INFO - ===== Starting Modern Sonarr TV Show Download Monitoring with SQLite =====
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,339 - pipeline_02 - INFO -      ENHANCED: Dual-detection system (Filesystem + Sonarr API) + SQLite state
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,340 - _internal.utils.filesystem_first_state_manager - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,340 - pipeline_02 - INFO - Discovering TV shows by scanning filesystem...
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,342 - pipeline_02 - INFO - Found 0 content items across 14 stages
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,342 - pipeline_02 - INFO -      SABnzbd complete directory: workspace\1_downloading\complete_raw
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,342 - pipeline_02 - INFO -      Sonarr API endpoint: http://localhost:8989
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,342 - pipeline_02 - INFO -      SMART STATE VALIDATION: Checking for inconsistent TV show states...
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,349 - pipeline_02 - INFO - Retrieved 2 TV series from Sonarr
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,351 - pipeline_02 - INFO -      Active TV show downloads in Sonarr queue: 10
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,352 - pipeline_02 - INFO -      ENHANCED: Checking both Sonarr API and filesystem for completed TV shows
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,352 - pipeline_02 - INFO -      Will scan SABnzbd directory: workspace\1_downloading\complete_raw
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,352 - pipeline_02 - INFO - Selected main video file: Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (0.83 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,352 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~0.83 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,353 - pipeline_02 - INFO - Selected main video file: Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (0.88 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,353 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~0.88 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,353 - pipeline_02 - INFO - Selected main video file: Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (0.84 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,353 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~0.84 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,353 - pipeline_02 - INFO - Selected main video file: Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (0.83 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,353 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~0.83 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,354 - pipeline_02 - INFO - Selected main video file: Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (0.94 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,354 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~0.94 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,354 - pipeline_02 - INFO - Selected main video file: Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (0.99 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,354 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~0.99 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,355 - pipeline_02 - INFO - Selected main video file: Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (0.87 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,355 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~0.87 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,355 - pipeline_02 - INFO - Selected main video file: Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (0.94 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,355 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~0.94 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,355 - pipeline_02 - INFO - Selected main video file: Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso.mkv (1.02 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,355 - pipeline_02 - INFO -      Found completed TV folder: Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso (1 video files, ~1.02 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,356 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S01E47.720p.BluRay.x264-TAXES.mkv (0.21 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,356 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S01E47.720p.BluRay.x264-TAXES (1 video files, ~0.21 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,356 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S01E48.720p.BluRay.x264-TAXES.mkv (0.21 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,357 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S01E48.720p.BluRay.x264-TAXES (1 video files, ~0.21 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,357 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E11.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,357 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E11.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,357 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E12.1080p.BluRay.x264-TAXES.mkv (0.55 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,357 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E12.1080p.BluRay.x264-TAXES (1 video files, ~0.55 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,358 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E13.1080p.BluRay.x264-TAXES.mkv (0.55 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,358 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E13.1080p.BluRay.x264-TAXES (1 video files, ~0.55 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,358 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E14.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,358 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E14.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,358 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E15.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,358 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E15.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,359 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E16.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,359 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E16.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,359 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E17.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,359 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E17.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,359 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E18.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,359 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E18.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,359 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E19.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,360 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E19.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,360 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E20.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,360 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E20.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,360 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E21.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,360 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E21.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,360 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E22.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,361 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E22.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,361 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E23.1080p.BluRay.x264-TAXES.mkv (0.55 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,361 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E23.1080p.BluRay.x264-TAXES (1 video files, ~0.55 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,361 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E24.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,361 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E24.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,362 - pipeline_02 - INFO - Selected main video file: Steven.Universe.S02E25.1080p.BluRay.x264-TAXES.mkv (0.54 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,362 - pipeline_02 - INFO -      Found completed TV folder: Steven.Universe.S02E25.1080p.BluRay.x264-TAXES (1 video files, ~0.54 GB)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,384 - pipeline_02 - INFO -      FILESYSTEM DETECTION: Found 26 completed TV folders ready for organization
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,384 - pipeline_02 - INFO -      Processing 26 completed TV folders
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,384 - pipeline_02 - INFO -      ⚠️ Skipping Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso - processing lock present
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,384 - pipeline_02 - INFO -      ⚠️ Skipping Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso - processing lock present
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,385 - pipeline_02 - INFO -      ⚠️ Skipping Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso - processing lock present
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,385 - pipeline_02 - INFO -      ⚠️ Skipping Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso - processing lock present
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,385 - pipeline_02 - INFO -      ⚠️ Skipping Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso - processing lock present
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,385 - pipeline_02 - INFO -      ⚠️ Skipping Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso - processing lock present
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,385 - pipeline_02 - INFO -      ⚠️ Skipping Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso - processing lock present
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,385 - pipeline_02 - INFO -      ⚠️ Skipping Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso - processing lock present
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,386 - pipeline_02 - INFO -      ⚠️ Skipping Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso - processing lock present
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,386 - pipeline_02 - INFO -      ⚠️ Skipping Steven.Universe.S01E47.720p.BluRay.x264-TAXES - processing lock present
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,386 - pipeline_02 - INFO -      ⚠️ Skipping Steven.Universe.S01E48.720p.BluRay.x264-TAXES - processing lock present
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,386 - pipeline_02 - INFO -      ⚠️ Skipping Steven.Universe.S02E11.1080p.BluRay.x264-TAXES - processing lock present
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,386 - pipeline_02 - INFO -      ⚠️ Skipping Steven.Universe.S02E12.1080p.BluRay.x264-TAXES - processing lock present
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,386 - pipeline_02 - INFO -      ⚠️ Skipping Steven.Universe.S02E13.1080p.BluRay.x264-TAXES - processing lock present
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,386 - pipeline_02 - INFO -      ⚠️ Skipping Steven.Universe.S02E14.1080p.BluRay.x264-TAXES - processing lock present
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,386 - pipeline_02 - INFO -      ⚠️ Skipping Steven.Universe.S02E15.1080p.BluRay.x264-TAXES - processing lock present
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,387 - pipeline_02 - INFO -      ⚠️ Skipping Steven.Universe.S02E16.1080p.BluRay.x264-TAXES - processing lock present
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,387 - pipeline_02 - INFO -      ⚠️ Skipping Steven.Universe.S02E17.1080p.BluRay.x264-TAXES - processing lock present
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,387 - pipeline_02 - INFO -      ⚠️ Skipping Steven.Universe.S02E18.1080p.BluRay.x264-TAXES - processing lock present
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,387 - pipeline_02 - INFO -      ⚠️ Skipping Steven.Universe.S02E19.1080p.BluRay.x264-TAXES - processing lock present
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,388 - pipeline_02 - INFO -      ⚠️ Skipping Steven.Universe.S02E20.1080p.BluRay.x264-TAXES - processing lock present
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,388 - pipeline_02 - INFO -      ⚠️ Skipping Steven.Universe.S02E21.1080p.BluRay.x264-TAXES - processing lock present
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,388 - pipeline_02 - INFO -      ⚠️ Skipping Steven.Universe.S02E22.1080p.BluRay.x264-TAXES - processing lock present
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,388 - pipeline_02 - INFO -      ⚠️ Skipping Steven.Universe.S02E23.1080p.BluRay.x264-TAXES - processing lock present
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,388 - pipeline_02 - INFO -      ⚠️ Skipping Steven.Universe.S02E24.1080p.BluRay.x264-TAXES - processing lock present
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,388 - pipeline_02 - INFO -      ⚠️ Skipping Steven.Universe.S02E25.1080p.BluRay.x264-TAXES - processing lock present
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,388 - pipeline_02 - INFO - 🔄 Checking for dynamic season progression opportunities...
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,389 - pipeline_02 - INFO -      Sequential progression enabled for 0 series
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,389 - pipeline_02 - INFO -      No series opted-in for sequential progression
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,389 - pipeline_02 - INFO - ===== Finished Modern Sonarr TV Show Download Monitoring =====
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,389 - pipeline_02 - INFO -     ✅ Pipeline 02 completed successfully (TV Shows)
[2025-09-18 14:44:56] [STDERR] [+0:00:00] 2025-09-18 14:44:56,389 - pipeline_02 - INFO - ===== Finished Pipeline 02 Execution =====
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] ------------------------------------------------------------
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] 🏁 Terminal logging ended for 02_download_and_organize
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] 🕐 Ended at: 2025-09-18 14:44:56
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] ⏱️ Total duration: 0:00:00.057060
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] 
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] 📄 Log saved to: C:\Users\<USER>\Videos\PlexAutomator\logs\02_download_and_organize_2025-09-18_02-44-56-PM.txt
[2025-09-18 14:44:56] [STDOUT] [+0:00:00] 


==================================================
=== TERMINAL OUTPUT LOG END ===
Script: 02_download_and_organize
Ended: 2025-09-18 14:44:56
Duration: 0:00:00.057060
==================================================
