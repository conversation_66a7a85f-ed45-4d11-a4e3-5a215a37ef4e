=== TERMINAL OUTPUT LOG ===
Script: 01_intake_and_nzb_search
Started: 2025-09-18 09:09:14
Log File: C:\Users\<USER>\Videos\PlexAutomator\logs\01_intake_and_nzb_search_2025-09-18_09-09-14-AM.txt
==================================================

[2025-09-18 09:09:14] [STDOUT] [+0:00:00] 📝 Terminal logging started for 01_intake_and_nzb_search
[2025-09-18 09:09:14] [STDOUT] [+0:00:00] 
[2025-09-18 09:09:14] [STDOUT] [+0:00:00] 📄 Log file: C:\Users\<USER>\Videos\PlexAutomator\logs\01_intake_and_nzb_search_2025-09-18_09-09-14-AM.txt
[2025-09-18 09:09:14] [STDOUT] [+0:00:00] 
[2025-09-18 09:09:14] [STDOUT] [+0:00:00] 🕐 Started at: 2025-09-18 09:09:14
[2025-09-18 09:09:14] [STDOUT] [+0:00:00] 
[2025-09-18 09:09:14] [STDOUT] [+0:00:00] ------------------------------------------------------------
[2025-09-18 09:09:14] [STDOUT] [+0:00:00] 
[2025-09-18 09:09:14] [STDERR] [+0:00:00] 2025-09-18 09:09:14,216 - interactive_pipeline_01 - INFO - ===== Starting Interactive Pipeline 01 Execution =====
[2025-09-18 09:09:14] [STDOUT] [+0:00:00] INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexAutomator\_internal\config\settings.ini
[2025-09-18 09:09:14] [STDOUT] [+0:00:00] 
[2025-09-18 09:09:14] [STDERR] [+0:00:00] 2025-09-18 09:09:14,218 - interactive_pipeline_01 - INFO - Settings loaded successfully
[2025-09-18 09:09:14] [STDERR] [+0:00:00] 2025-09-18 09:09:14,218 - interactive_pipeline_01 - INFO - Configuration: max_candidates=50, quality_fallback=True, telemetry_verbose=False
[2025-09-18 09:09:14] [STDERR] [+0:00:00] 2025-09-18 09:09:14,218 - interactive_pipeline_01 - INFO - 🔄 Real-time telemetry system initialized
[2025-09-18 09:09:14] [STDERR] [+0:00:00] 2025-09-18 09:09:14,218 - interactive_pipeline_01 - INFO - 🔬 Enhanced telemetry integration initialized
[2025-09-18 09:09:14] [STDERR] [+0:00:00] 2025-09-18 09:09:14,218 - interactive_pipeline_01 - INFO -    📊 Loaded 8 existing movie records
[2025-09-18 09:09:14] [STDERR] [+0:00:00] 2025-09-18 09:09:14,218 - interactive_pipeline_01 - INFO - 🔬 Real-time telemetry initialized EARLY - ready for immediate monitoring
[2025-09-18 09:09:14] [STDOUT] [+0:00:00] 🔬 Real-time download monitoring enabled (dashboard mode) - will start monitoring as soon as first download begins
[2025-09-18 09:09:14] [STDOUT] [+0:00:00] 
[2025-09-18 09:09:14] [STDOUT] [+0:00:00] 
[2025-09-18 09:09:14] [STDOUT] [+0:00:00] ============================================================
[2025-09-18 09:09:14] [STDOUT] [+0:00:00] 
[2025-09-18 09:09:14] [STDOUT] [+0:00:00] 🎬📺 PlexMovieAutomator - Interactive Content Selection
[2025-09-18 09:09:14] [STDOUT] [+0:00:00] 
[2025-09-18 09:09:14] [STDOUT] [+0:00:00] ============================================================
[2025-09-18 09:09:14] [STDOUT] [+0:00:00] 
[2025-09-18 09:09:14] [STDOUT] [+0:00:00] 
[2025-09-18 09:09:14] [STDOUT] [+0:00:00] What type of content would you like to process?
[2025-09-18 09:09:14] [STDOUT] [+0:00:00] 
[2025-09-18 09:09:14] [STDOUT] [+0:00:00]   1. Movies only
[2025-09-18 09:09:14] [STDOUT] [+0:00:00] 
[2025-09-18 09:09:14] [STDOUT] [+0:00:00]   2. TV Shows only
[2025-09-18 09:09:14] [STDOUT] [+0:00:00] 
[2025-09-18 09:09:14] [STDOUT] [+0:00:00]   3. Both Movies and TV Shows
[2025-09-18 09:09:14] [STDOUT] [+0:00:00] 
[2025-09-18 09:09:14] [STDOUT] [+0:00:00]   4. Quit
[2025-09-18 09:09:14] [STDOUT] [+0:00:00] 
[2025-09-18 09:09:16] [STDOUT] [+0:00:02] 
[2025-09-18 09:09:16] [STDOUT] [+0:00:02] ============================================================
[2025-09-18 09:09:16] [STDOUT] [+0:00:02] 
[2025-09-18 09:09:16] [STDOUT] [+0:00:02] 🤖 Processing Mode Selection
[2025-09-18 09:09:16] [STDOUT] [+0:00:02] 
[2025-09-18 09:09:16] [STDOUT] [+0:00:02] ============================================================
[2025-09-18 09:09:16] [STDOUT] [+0:00:02] 
[2025-09-18 09:09:16] [STDOUT] [+0:00:02] 
[2025-09-18 09:09:16] [STDOUT] [+0:00:02] How would you like to handle download decisions?
[2025-09-18 09:09:16] [STDOUT] [+0:00:02] 
[2025-09-18 09:09:16] [STDOUT] [+0:00:02]   1. 🖱️  Manual Mode - Choose options for each movie/show individually
[2025-09-18 09:09:16] [STDOUT] [+0:00:02] 
[2025-09-18 09:09:16] [STDOUT] [+0:00:02]   2. 🤖 Full Auto Mode - Automatically use preflight analysis with max candidates
[2025-09-18 09:09:16] [STDOUT] [+0:00:02] 
[2025-09-18 09:09:16] [STDOUT] [+0:00:02] 
[2025-09-18 09:09:16] [STDOUT] [+0:00:02] 📝 Full Auto Mode Details:
[2025-09-18 09:09:16] [STDOUT] [+0:00:02] 
[2025-09-18 09:09:16] [STDOUT] [+0:00:02]    • Automatically selects preflight analysis for every item
[2025-09-18 09:09:16] [STDOUT] [+0:00:02] 
[2025-09-18 09:09:16] [STDOUT] [+0:00:02]    • Automatically chooses max candidates when prompted
[2025-09-18 09:09:16] [STDOUT] [+0:00:02] 
[2025-09-18 09:09:16] [STDOUT] [+0:00:02]    • No manual intervention required - perfect for overnight processing
[2025-09-18 09:09:16] [STDOUT] [+0:00:02] 
[2025-09-18 09:09:16] [STDOUT] [+0:00:02]    • Falls back gracefully if preflight fails
[2025-09-18 09:09:16] [STDOUT] [+0:00:02] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] ✅ Full Auto Mode selected - processing will run unattended
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 📁 Loaded 12 tv_shows from C:\Users\<USER>\Videos\PlexAutomator\new_tv_requests.txt
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] ======================================================================
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 📺 TV Shows Available for Processing:
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] ======================================================================
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]    1. Ed, Edd n Eddy (1999)                    📚 Complete Series        
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]       📋 Will use TVDB for chronological episode tracking
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]    2. Adventure Time (2010)                    📚 Complete Series        
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]       📋 Will use TVDB for chronological episode tracking
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]    3. The Saddle Club (2003)                   📚 Complete Series        
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]       📋 Will use TVDB for chronological episode tracking
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]    4. Samurai Jack (2001) S01                  📀 Season S01             
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]       📋 Will use TVDB for chronological episode tracking
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]    5. Steven Universe (2013) S02               📀 Season S02             
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]       📋 Will use TVDB for chronological episode tracking
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]    6. Futurama (1999) S01                      📀 Season S01             
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]       📋 Will use TVDB for chronological episode tracking
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]    7. The Powerpuff Girls (1998)               📚 Complete Series        
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]       📋 Will use TVDB for chronological episode tracking
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]    8. Regular Show (2010) S08E31               📺 Episode S08E31         
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]    9. Dexter's Laboratory (1996) S01E01, S01E05, S01E12 📺 Multi-Episodes S01E01, S01E05, S01E12
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]   10. Teen Titans (2003) S02E03, S02E07, S02E13 📺 Multi-Episodes S02E03, S02E07, S02E13
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]   11. Johnny Bravo (1997) S01E01, S03E15       📺 Multi-Episodes S01E01, S03E15
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]   12. Ben 10 (2005) S01E01, S02E13, S04E21     📺 Multi-Episodes S01E01, S02E13, S04E21
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 📊 Legend:
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]    📺 Episode    - Single episode download
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]    📀 Season     - Full season download (all episodes)
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]    📚 Series     - Complete series (all seasons)
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 📝 Selection Options:
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]   • Single: Enter number (e.g., '3')
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]   • Multiple: Enter comma-separated numbers (e.g., '1,3,5')
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]   • All: Enter 'all' or 'a'
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]   • None: Enter 'none' or 'n' to skip
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:23] [STDOUT] [+0:00:08]   • Quit: Enter 'quit' or 'q'
[2025-09-18 09:09:23] [STDOUT] [+0:00:08] 
[2025-09-18 09:09:35] [STDOUT] [+0:00:21] 
[2025-09-18 09:09:35] [STDOUT] [+0:00:21] ✅ Selected 2 TV shows:
[2025-09-18 09:09:35] [STDOUT] [+0:00:21] 
[2025-09-18 09:09:35] [STDOUT] [+0:00:21]     1. 📀 Steven Universe (2013) S02
[2025-09-18 09:09:35] [STDOUT] [+0:00:21] 
[2025-09-18 09:09:35] [STDOUT] [+0:00:21]     2. 📀 Futurama (1999) S01
[2025-09-18 09:09:35] [STDOUT] [+0:00:21] 
[2025-09-18 09:09:38] [STDOUT] [+0:00:24] 
[2025-09-18 09:09:38] [STDOUT] [+0:00:24] 📺 Processing 2 selected TV shows...
[2025-09-18 09:09:38] [STDOUT] [+0:00:24] 
[2025-09-18 09:09:38] [STDOUT] [+0:00:24] ============================================================
[2025-09-18 09:09:38] [STDOUT] [+0:00:24] 
[2025-09-18 09:09:38] [STDERR] [+0:00:24] 2025-09-18 09:09:38,545 - _internal.utils.filesystem_first_state_manager - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexAutomator\_internal\data\movie_metadata.db
[2025-09-18 09:09:38] [STDOUT] [+0:00:24] 
[2025-09-18 09:09:38] [STDOUT] [+0:00:24] 📍 Progress: 1/2
[2025-09-18 09:09:38] [STDOUT] [+0:00:24] 
[2025-09-18 09:09:38] [STDOUT] [+0:00:24] 📺 Processing: Steven Universe (2013) S02
[2025-09-18 09:09:38] [STDOUT] [+0:00:24] 
[2025-09-18 09:09:38] [STDOUT] [+0:00:24]    🎯 Request Type: Specific Season
[2025-09-18 09:09:38] [STDOUT] [+0:00:24] 
[2025-09-18 09:09:38] [STDOUT] [+0:00:24]    📀 Target: S02 (all episodes)
[2025-09-18 09:09:38] [STDOUT] [+0:00:24] 
[2025-09-18 09:09:38] [STDERR] [+0:00:24] 2025-09-18 09:09:38,546 - interactive_pipeline_01 - INFO - Processing TV show: Steven Universe (2013) S02 (specific_season)
[2025-09-18 09:09:38] [STDERR] [+0:00:24] 2025-09-18 09:09:38,707 - _internal.src.metadata_fetcher - INFO - TMDb TV search with year 2013: 1 results
[2025-09-18 09:09:38] [STDERR] [+0:00:24] 2025-09-18 09:09:38,866 - _internal.utils.fuzzy_matching - INFO - Found 1 exact matches for 'Steven Universe', prioritizing them
[2025-09-18 09:09:39] [STDERR] [+0:00:24] 2025-09-18 09:09:39,099 - _internal.src.metadata_fetcher - ERROR - Error during TV fuzzy matching for 'Steven Universe': EnhancedFuzzyMatchingConfig.get_year_tolerance() missing 1 required positional argument: 'content_type'
[2025-09-18 09:09:39] [STDOUT] [+0:00:24] ✅ Found metadata: Steven Universe (2013)
[2025-09-18 09:09:39] [STDOUT] [+0:00:24] 
[2025-09-18 09:09:39] [STDERR] [+0:00:24] 2025-09-18 09:09:39,148 - interactive_pipeline_01 - INFO - Successfully found TV metadata for: Steven Universe
[2025-09-18 09:09:39] [STDOUT] [+0:00:24] 🔍 Fetching complete episode data from TVDB...
[2025-09-18 09:09:39] [STDOUT] [+0:00:24] 
[2025-09-18 09:09:39] [STDERR] [+0:00:24] 2025-09-18 09:09:39,148 - interactive_pipeline_01 - INFO - Fetching TVDB episode data for: Steven Universe
[2025-09-18 09:09:39] [STDOUT] [+0:00:25] 🔍 DEBUG: Data source: TMDb TV API (fallback from TVDB)
[2025-09-18 09:09:39] [STDOUT] [+0:00:25] 
[2025-09-18 09:09:39] [STDOUT] [+0:00:25] 🔍 DEBUG: Total seasons reported: 1
[2025-09-18 09:09:39] [STDOUT] [+0:00:25] 
[2025-09-18 09:09:39] [STDOUT] [+0:00:25] 🔍 DEBUG: Total episodes reported: 1
[2025-09-18 09:09:39] [STDOUT] [+0:00:25] 
[2025-09-18 09:09:39] [STDOUT] [+0:00:25] 🔍 DEBUG: Season 1: 1 episodes
[2025-09-18 09:09:39] [STDOUT] [+0:00:25] 
[2025-09-18 09:09:39] [STDOUT] [+0:00:25] 🔍 DEBUG: Season 1 episodes: ['1']
[2025-09-18 09:09:39] [STDOUT] [+0:00:25] 
[2025-09-18 09:09:39] [STDOUT] [+0:00:25] ✅ Found complete series data: 1 seasons, 1 episodes
[2025-09-18 09:09:39] [STDOUT] [+0:00:25] 
[2025-09-18 09:09:39] [STDERR] [+0:00:25] 2025-09-18 09:09:39,648 - interactive_pipeline_01 - INFO - TVDB data retrieved: 1 seasons, 1 episodes
[2025-09-18 09:09:39] [STDERR] [+0:00:25] 2025-09-18 09:09:39,648 - interactive_pipeline_01 - INFO - Created season queue: 0 episodes from seasons [2]
[2025-09-18 09:09:39] [STDOUT] [+0:00:25] ⚠️ No episodes found for download queue
[2025-09-18 09:09:39] [STDOUT] [+0:00:25] 
[2025-09-18 09:09:39] [STDERR] [+0:00:25] 2025-09-18 09:09:39,652 - interactive_pipeline_01 - INFO - 🧪 Skipping creation of season pack blocking profile (preflight handles pack decisions)
[2025-09-18 09:09:39] [STDERR] [+0:00:25] 2025-09-18 09:09:39,652 - interactive_pipeline_01 - INFO - 📺 TV Quality Strategy (ADAPTIVE): Adaptive Quality: Using inclusive profile (ID 6) - prevents episode skipping, allows best available quality selection
[2025-09-18 09:09:39] [STDERR] [+0:00:25] 2025-09-18 09:09:39,652 - interactive_pipeline_01 - INFO - 📺 Year-based preference hint: 2013 (used for internal logic, not restrictions)
[2025-09-18 09:09:39] [STDERR] [+0:00:25] 2025-09-18 09:09:39,653 - interactive_pipeline_01 - INFO - Searching Sonarr for: Steven Universe 2013
[2025-09-18 09:09:42] [STDERR] [+0:00:28] 2025-09-18 09:09:42,353 - interactive_pipeline_01 - INFO - Selected TV show: Steven Universe (2013) | tvdbId=270701 | alternatives: [{'title': 'Father Brown (2013)', 'year': 2013, 'tvdbId': 265834, 'score': 339}, {'title': 'House of Cards (US)', 'year': 2013, 'tvdbId': 262980, 'score': 331}, {'title': 'The Americans (2013)', 'year': 2013, 'tvdbId': 261690, 'score': 321}]
[2025-09-18 09:09:42] [STDERR] [+0:00:28] 2025-09-18 09:09:42,356 - interactive_pipeline_01 - INFO - 📁 Matched existing Sonarr root folder: E:\
[2025-09-18 09:09:42] [STDERR] [+0:00:28] 2025-09-18 09:09:42,356 - interactive_pipeline_01 - INFO - 📋 Adaptive Quality: Using inclusive profile (ID 6) - prevents episode skipping, allows best available quality selection
[2025-09-18 09:09:42] [STDERR] [+0:00:28] 2025-09-18 09:09:42,356 - interactive_pipeline_01 - INFO - 📺 Adding TV show to Sonarr with 1 quality profile(s): Steven Universe
[2025-09-18 09:09:42] [STDERR] [+0:00:28] 2025-09-18 09:09:42,359 - interactive_pipeline_01 - INFO -    📥 Adding with quality profile 6...
[2025-09-18 09:09:42] [STDERR] [+0:00:28] 2025-09-18 09:09:42,477 - interactive_pipeline_01 - INFO -    ✅ Added: Steven Universe (Series ID 369, Profile 6)
[2025-09-18 09:09:42] [STDERR] [+0:00:28] 2025-09-18 09:09:42,485 - interactive_pipeline_01 - INFO -    🎯 Configured monitoring for Season 2
[2025-09-18 09:09:42] [STDERR] [+0:00:28] 2025-09-18 09:09:42,486 - interactive_pipeline_01 - WARNING -    ⚠️ No episodes found for Season 2
[2025-09-18 09:09:42] [STDERR] [+0:00:28] 2025-09-18 09:09:42,486 - interactive_pipeline_01 - INFO - 📏 Max episode size threshold configured: 40.0 GB
[2025-09-18 09:09:47] [STDERR] [+0:00:33] 2025-09-18 09:09:47,499 - interactive_pipeline_01 - INFO - Episode size enforcement: no oversized items detected
[2025-09-18 09:09:47] [STDERR] [+0:00:33] 2025-09-18 09:09:47,499 - interactive_pipeline_01 - INFO - ✅ Season pack evaluation handled by preflight analyzer
[2025-09-18 09:09:47] [STDOUT] [+0:00:33] 📥 Queued "Steven Universe (2013)" for download...
[2025-09-18 09:09:47] [STDOUT] [+0:00:33] 
[2025-09-18 09:09:47] [STDERR] [+0:00:33] 2025-09-18 09:09:47,503 - interactive_pipeline_01 - INFO - {"timestamp": "2025-09-18T09:09:47.503282", "event": "download_queued", "job_id": "sonarr_369_series", "title": "Steven Universe (2013)", "source": "sonarr", "status": "pending", "progress": 0.0, "size_total": 0, "size_downloaded": 0, "speed_bps": 0.0, "eta": "Unknown", "sonarr_id": 369, "quality": "Unknown"}
[2025-09-18 09:09:47] [STDOUT] [+0:00:33] 📊 TV show queued for download: Steven Universe (2013)
[2025-09-18 09:09:47] [STDOUT] [+0:00:33] 
[2025-09-18 09:09:47] [STDOUT] [+0:00:33]    🔬 Real-time tracking: sonarr_3...
[2025-09-18 09:09:47] [STDOUT] [+0:00:33] 
[2025-09-18 09:09:47] [STDOUT] [+0:00:33]    📺 Series-wide tracking enabled
[2025-09-18 09:09:47] [STDOUT] [+0:00:33] 
[2025-09-18 09:09:47] [STDERR] [+0:00:33] 2025-09-18 09:09:47,504 - interactive_pipeline_01 - INFO - Telemetry job started: sonarr_369_series for series 369
[2025-09-18 09:09:47] [STDOUT] [+0:00:33]    📀 Configured for: S02 (all episodes)
[2025-09-18 09:09:47] [STDOUT] [+0:00:33] 
[2025-09-18 09:09:47] [STDOUT] [+0:00:33] 
[2025-09-18 09:09:47] [STDOUT] [+0:00:33] 🔍 Alternative candidate matches (top scoring):
[2025-09-18 09:09:47] [STDOUT] [+0:00:33] 
[2025-09-18 09:09:47] [STDOUT] [+0:00:33]    • Father Brown (2013) (2013) tvdb:265834 score:339
[2025-09-18 09:09:47] [STDOUT] [+0:00:33] 
[2025-09-18 09:09:47] [STDOUT] [+0:00:33]    • House of Cards (US) (2013) tvdb:262980 score:331
[2025-09-18 09:09:47] [STDOUT] [+0:00:33] 
[2025-09-18 09:09:47] [STDOUT] [+0:00:33]    • The Americans (2013) (2013) tvdb:261690 score:321
[2025-09-18 09:09:47] [STDOUT] [+0:00:33] 
[2025-09-18 09:09:47] [STDERR] [+0:00:33] 2025-09-18 09:09:47,505 - interactive_pipeline_01 - INFO - ✅ Successfully added TV show to Sonarr: Steven Universe
[2025-09-18 09:09:47] [STDOUT] [+0:00:33] 🤖 Auto Mode: Using Preflight Analysis for Steven Universe
[2025-09-18 09:09:47] [STDOUT] [+0:00:33] 
[2025-09-18 09:09:47] [STDOUT] [+0:00:33] 🔬 Using Preflight Analysis for Steven Universe
[2025-09-18 09:09:47] [STDOUT] [+0:00:33] 
[2025-09-18 09:09:47] [STDERR] [+0:00:33] 2025-09-18 09:09:47,506 - interactive_pipeline_01 - INFO - User selected preflight analysis for: Steven Universe
[2025-09-18 09:09:47] [STDOUT] [+0:00:33] 🔎 Preflight analyzing 1 season(s): [2]
[2025-09-18 09:09:47] [STDOUT] [+0:00:33] 
[2025-09-18 09:09:47] [STDOUT] [+0:00:33]    Season 2: All episodes
[2025-09-18 09:09:47] [STDOUT] [+0:00:33] 
[2025-09-18 09:09:55] [STDOUT] [+0:00:41] 
[2025-09-18 09:09:55] [STDOUT] [+0:00:41] 🎯 Analyzing Season 2...
[2025-09-18 09:09:55] [STDOUT] [+0:00:41] 
[2025-09-18 09:09:55] [STDOUT] [+0:00:41] 🆕 No existing decision found for Season 2, running fresh analysis
[2025-09-18 09:09:55] [STDOUT] [+0:00:41] 
[2025-09-18 09:09:55] [STDERR] [+0:00:41] 2025-09-18 09:09:55,605 - preflight_analyzer.tv_show_preflight_selector - INFO - 🎬 Starting TV preflight analysis - Series: 369, Episodes: 29, Mode: standard
[2025-09-18 09:09:55] [STDERR] [+0:00:41] 2025-09-18 09:09:55,606 - preflight_analyzer.tv_show_preflight_selector - INFO - 📋 Analysis configuration - Cache: True, Deduplicate: True, Fresh checks: False
[2025-09-18 09:09:55] [STDERR] [+0:00:41] 2025-09-18 09:09:55,606 - preflight_analyzer.cache_observability - INFO - Initialized enhanced cache metrics with max_events=10000
[2025-09-18 09:09:55] [STDERR] [+0:00:41] 2025-09-18 09:09:55,606 - preflight_analyzer.memory_cache - INFO - Initialized memory cache with maxsize=1000, ttl=43200s
[2025-09-18 09:09:55] [STDERR] [+0:00:41] 2025-09-18 09:09:55,615 - preflight_analyzer.persistent_cache - INFO - Initialized persistent cache at workspace\preflight_cache\cache\analysis_cache.db
[2025-09-18 09:09:55] [STDERR] [+0:00:41] 2025-09-18 09:09:55,615 - preflight_analyzer.guid_reconciler - INFO - Initialized GUID reconciler with size_tolerance=0.05, title_threshold=0.8, min_confidence=0.7, groups=True, indexers=True
[2025-09-18 09:09:55] [STDERR] [+0:00:41] 2025-09-18 09:09:55,615 - preflight_analyzer.ttl_coordinator - INFO - Initialized TTL coordinator with default policies
[2025-09-18 09:09:55] [STDERR] [+0:00:41] 2025-09-18 09:09:55,616 - preflight_analyzer.multi_layer_cache - INFO - Initialized multi-layer cache at workspace\preflight_cache\cache
[2025-09-18 09:09:55] [STDERR] [+0:00:41] 2025-09-18 09:09:55,616 - preflight_analyzer.cache - INFO - Initialized decision cache at workspace\preflight_cache\cache
[2025-09-18 09:09:55] [STDERR] [+0:00:41] 2025-09-18 09:09:55,637 - preflight_analyzer.tv_show_preflight_selector - INFO - 📺 Analyzing 29 episodes from Steven Universe Season 2
[2025-09-18 09:09:55] [STDERR] [+0:00:41] 2025-09-18 09:09:55,637 - preflight_analyzer.tv_show_preflight_selector - INFO - 📺 Analyzing 29 episodes in parallel (max 6 concurrent)
[2025-09-18 09:09:55] [STDERR] [+0:00:41] 2025-09-18 09:09:55,929 - preflight_analyzer.tv_show_preflight_selector - INFO -    🔍 09:09:55 Analyzing: Steven.Universe.S02E01.Full.Disclosure.1080p.BluRay.Opus.2.0.x265-edge2020
[2025-09-18 09:09:56] [STDERR] [+0:00:42] 2025-09-18 09:09:56,312 - preflight_analyzer.tv_show_preflight_selector - INFO -    🔍 09:09:56 Analyzing: Steven.Universe.S02E01.720p.BluRay.x264-TAXES
[2025-09-18 09:09:56] [STDERR] [+0:00:42] 2025-09-18 09:09:56,694 - preflight_analyzer.tv_show_preflight_selector - INFO -    🔍 09:09:56 Analyzing: Steven.Universe.S02E01.1080p.BluRay.x264-TAXES
[2025-09-18 09:09:57] [STDERR] [+0:00:42] 2025-09-18 09:09:57,152 - preflight_analyzer.tv_show_preflight_selector - INFO -    🔍 09:09:57 Analyzing: Steven.Universe.S02E01.1080p.BluRay.x264-TAXES
[2025-09-18 09:10:05] [STDERR] [+0:00:50] 2025-09-18 09:10:05,198 - preflight_analyzer.tv_show_preflight_selector - INFO -    ✅ 09:10:05 Result: ACCEPT (risk: 0.0218, missing: 0.0%)
[2025-09-18 09:10:09] [STDERR] [+0:00:54] 2025-09-18 09:10:09,172 - preflight_analyzer.tv_show_preflight_selector - INFO -    ✅ 09:10:09 Result: ACCEPT (risk: 0.1324, missing: 0.5%)
[2025-09-18 09:10:10] [STDERR] [+0:00:56] 2025-09-18 09:10:10,739 - preflight_analyzer.tv_show_preflight_selector - INFO -    ✅ 09:10:10 Result: ACCEPT (risk: 0.1312, missing: 0.0%)
[2025-09-18 09:10:11] [STDERR] [+0:00:57] 2025-09-18 09:10:11,368 - preflight_analyzer.tv_show_preflight_selector - INFO -    ✅ 09:10:11 Result: ACCEPT (risk: 0.1322, missing: 0.4%)
[2025-09-18 09:10:11] [STDERR] [+0:00:57] 2025-09-18 09:10:11,728 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: cc9332c1-f2d8-4e6b-9974-7ebf8df9d64f -> movie:unknown
[2025-09-18 09:10:11] [STDERR] [+0:00:57] 2025-09-18 09:10:11,728 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: cc9332c1..., 1.4ms)
[2025-09-18 09:10:11] [STDERR] [+0:00:57] 2025-09-18 09:10:11,728 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: cc9332c1..., 1.4ms)
[2025-09-18 09:10:11] [STDERR] [+0:00:57] 2025-09-18 09:10:11,728 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:11 Cache hit: Steven.Universe.S01E46.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:11] [STDERR] [+0:00:57] 2025-09-18 09:10:11,768 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 3a80879a-4090-4f98-9f80-4e33afae9e6f -> movie:unknown
[2025-09-18 09:10:11] [STDERR] [+0:00:57] 2025-09-18 09:10:11,768 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 3a80879a..., 39.7ms)
[2025-09-18 09:10:11] [STDERR] [+0:00:57] 2025-09-18 09:10:11,768 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 3a80879a..., 39.7ms)
[2025-09-18 09:10:11] [STDERR] [+0:00:57] 2025-09-18 09:10:11,769 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:11 Cache hit: Steven.Universe.S01E46.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:13] [STDERR] [+0:00:59] 2025-09-18 09:10:13,679 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 9bc37f09-d82a-47c6-b559-05d7d4dfb07a -> movie:unknown
[2025-09-18 09:10:13] [STDERR] [+0:00:59] 2025-09-18 09:10:13,679 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9bc37f09..., 1.2ms)
[2025-09-18 09:10:13] [STDERR] [+0:00:59] 2025-09-18 09:10:13,679 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9bc37f09..., 1.2ms)
[2025-09-18 09:10:13] [STDERR] [+0:00:59] 2025-09-18 09:10:13,679 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:13 Cache hit: Steven.Universe.S02E02.Open.Book.1080p.BluRay.Opus.2.0.x265-edge2020 → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:13] [STDERR] [+0:00:59] 2025-09-18 09:10:13,680 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 441b61a9-d351-4515-9d27-e578704a56a4 -> movie:unknown
[2025-09-18 09:10:13] [STDERR] [+0:00:59] 2025-09-18 09:10:13,680 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 441b61a9..., 0.9ms)
[2025-09-18 09:10:13] [STDERR] [+0:00:59] 2025-09-18 09:10:13,680 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 441b61a9..., 0.9ms)
[2025-09-18 09:10:13] [STDERR] [+0:00:59] 2025-09-18 09:10:13,681 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:13 Cache hit: Steven.Universe.S02E02.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:13] [STDERR] [+0:00:59] 2025-09-18 09:10:13,681 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 661ff7b4-8f89-44aa-b3ad-43f68fcedd09 -> movie:unknown
[2025-09-18 09:10:13] [STDERR] [+0:00:59] 2025-09-18 09:10:13,681 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 661ff7b4..., 0.7ms)
[2025-09-18 09:10:13] [STDERR] [+0:00:59] 2025-09-18 09:10:13,681 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 661ff7b4..., 0.7ms)
[2025-09-18 09:10:13] [STDERR] [+0:00:59] 2025-09-18 09:10:13,682 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:13 Cache hit: Steven.Universe.S02E02.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:13] [STDERR] [+0:00:59] 2025-09-18 09:10:13,682 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: dc3d9885-c884-4c12-b193-75f8ec4e02ea -> movie:unknown
[2025-09-18 09:10:13] [STDERR] [+0:00:59] 2025-09-18 09:10:13,683 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: dc3d9885..., 0.9ms)
[2025-09-18 09:10:13] [STDERR] [+0:00:59] 2025-09-18 09:10:13,683 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: dc3d9885..., 0.9ms)
[2025-09-18 09:10:13] [STDERR] [+0:00:59] 2025-09-18 09:10:13,683 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:13 Cache hit: Steven.Universe.S02E02.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:15] [STDERR] [+0:01:01] 2025-09-18 09:10:15,721 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 1af9eae1-a4a3-4987-a59b-c4e459079c38 -> movie:unknown
[2025-09-18 09:10:15] [STDERR] [+0:01:01] 2025-09-18 09:10:15,721 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 1af9eae1..., 1.2ms)
[2025-09-18 09:10:15] [STDERR] [+0:01:01] 2025-09-18 09:10:15,721 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 1af9eae1..., 1.2ms)
[2025-09-18 09:10:15] [STDERR] [+0:01:01] 2025-09-18 09:10:15,721 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:15 Cache hit: Steven.Universe.S02E03.Joy.Ride.1080p.BluRay.Opus.2.0.x265-edge2020 → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:15] [STDERR] [+0:01:01] 2025-09-18 09:10:15,722 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: e18de746-40fa-474d-9bd7-f0ac5cd5493f -> movie:unknown
[2025-09-18 09:10:15] [STDERR] [+0:01:01] 2025-09-18 09:10:15,722 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: e18de746..., 0.9ms)
[2025-09-18 09:10:15] [STDERR] [+0:01:01] 2025-09-18 09:10:15,722 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: e18de746..., 0.9ms)
[2025-09-18 09:10:15] [STDERR] [+0:01:01] 2025-09-18 09:10:15,722 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:15 Cache hit: Steven.Universe.S02E03.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:15] [STDERR] [+0:01:01] 2025-09-18 09:10:15,723 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 2173760d-be3b-47f6-94e1-858b9ceeed88 -> movie:unknown
[2025-09-18 09:10:15] [STDERR] [+0:01:01] 2025-09-18 09:10:15,723 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2173760d..., 0.8ms)
[2025-09-18 09:10:15] [STDERR] [+0:01:01] 2025-09-18 09:10:15,723 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2173760d..., 0.8ms)
[2025-09-18 09:10:15] [STDERR] [+0:01:01] 2025-09-18 09:10:15,723 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:15 Cache hit: Steven.Universe.S02E03.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:15] [STDERR] [+0:01:01] 2025-09-18 09:10:15,724 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 9a43bf0d-543b-42b1-925f-54a3399efdbe -> movie:unknown
[2025-09-18 09:10:15] [STDERR] [+0:01:01] 2025-09-18 09:10:15,724 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9a43bf0d..., 0.7ms)
[2025-09-18 09:10:15] [STDERR] [+0:01:01] 2025-09-18 09:10:15,724 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9a43bf0d..., 0.7ms)
[2025-09-18 09:10:15] [STDERR] [+0:01:01] 2025-09-18 09:10:15,724 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:15 Cache hit: Steven.Universe.S02E03.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:17] [STDERR] [+0:01:03] 2025-09-18 09:10:17,725 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 288ff011-d5d6-4bd6-b28e-a91801c5afc2 -> movie:unknown
[2025-09-18 09:10:17] [STDERR] [+0:01:03] 2025-09-18 09:10:17,725 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 288ff011..., 1.2ms)
[2025-09-18 09:10:17] [STDERR] [+0:01:03] 2025-09-18 09:10:17,725 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 288ff011..., 1.2ms)
[2025-09-18 09:10:17] [STDERR] [+0:01:03] 2025-09-18 09:10:17,726 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:17 Cache hit: Steven.Universe.S01E48.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:17] [STDERR] [+0:01:03] 2025-09-18 09:10:17,727 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: d161e5f9-f084-4e6b-9951-f4d5bd8952c2 -> movie:unknown
[2025-09-18 09:10:17] [STDERR] [+0:01:03] 2025-09-18 09:10:17,727 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: d161e5f9..., 1.1ms)
[2025-09-18 09:10:17] [STDERR] [+0:01:03] 2025-09-18 09:10:17,727 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: d161e5f9..., 1.1ms)
[2025-09-18 09:10:17] [STDERR] [+0:01:03] 2025-09-18 09:10:17,727 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:17 Cache hit: Steven.Universe.S01E48.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:19] [STDERR] [+0:01:05] 2025-09-18 09:10:19,816 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 013fc6bb-fcea-4648-8c17-7c8a1ed78547 -> movie:unknown
[2025-09-18 09:10:19] [STDERR] [+0:01:05] 2025-09-18 09:10:19,816 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 013fc6bb..., 1.4ms)
[2025-09-18 09:10:19] [STDERR] [+0:01:05] 2025-09-18 09:10:19,816 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 013fc6bb..., 1.4ms)
[2025-09-18 09:10:19] [STDERR] [+0:01:05] 2025-09-18 09:10:19,816 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:19 Cache hit: Steven.Universe.S01E47.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:19] [STDERR] [+0:01:05] 2025-09-18 09:10:19,817 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 5f693bce-75f7-477b-8336-8a57a4d3e047 -> movie:unknown
[2025-09-18 09:10:19] [STDERR] [+0:01:05] 2025-09-18 09:10:19,817 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 5f693bce..., 0.8ms)
[2025-09-18 09:10:19] [STDERR] [+0:01:05] 2025-09-18 09:10:19,817 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 5f693bce..., 0.8ms)
[2025-09-18 09:10:19] [STDERR] [+0:01:05] 2025-09-18 09:10:19,817 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:19 Cache hit: Steven.Universe.S01E47.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:21] [STDERR] [+0:01:07] 2025-09-18 09:10:21,742 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 8534fc60-7d47-4ff9-8817-a44f19c70da6 -> movie:unknown
[2025-09-18 09:10:21] [STDERR] [+0:01:07] 2025-09-18 09:10:21,743 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 8534fc60..., 1.4ms)
[2025-09-18 09:10:21] [STDERR] [+0:01:07] 2025-09-18 09:10:21,743 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 8534fc60..., 1.4ms)
[2025-09-18 09:10:21] [STDERR] [+0:01:07] 2025-09-18 09:10:21,743 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:21 Cache hit: Steven.Universe.S02E04.Say.Uncle.1080p.BluRay.Opus.2.0.x265-edge2020 → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:21] [STDERR] [+0:01:07] 2025-09-18 09:10:21,744 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 4e06a213-4366-43df-b946-614e21bc2829 -> movie:unknown
[2025-09-18 09:10:21] [STDERR] [+0:01:07] 2025-09-18 09:10:21,744 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 4e06a213..., 1.0ms)
[2025-09-18 09:10:21] [STDERR] [+0:01:07] 2025-09-18 09:10:21,744 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 4e06a213..., 1.0ms)
[2025-09-18 09:10:21] [STDERR] [+0:01:07] 2025-09-18 09:10:21,744 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:21 Cache hit: Steven.Universe.S02E04.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:21] [STDERR] [+0:01:07] 2025-09-18 09:10:21,745 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 7bd95553-fef7-4d5e-8ee4-2e0baa48dd7c -> movie:unknown
[2025-09-18 09:10:21] [STDERR] [+0:01:07] 2025-09-18 09:10:21,745 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 7bd95553..., 0.8ms)
[2025-09-18 09:10:21] [STDERR] [+0:01:07] 2025-09-18 09:10:21,745 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 7bd95553..., 0.8ms)
[2025-09-18 09:10:21] [STDERR] [+0:01:07] 2025-09-18 09:10:21,745 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:21 Cache hit: Steven.Universe.S02E04.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:21] [STDERR] [+0:01:07] 2025-09-18 09:10:21,746 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 3969f110-4228-4e62-8151-0e5a3247ae68 -> movie:unknown
[2025-09-18 09:10:21] [STDERR] [+0:01:07] 2025-09-18 09:10:21,746 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 3969f110..., 0.8ms)
[2025-09-18 09:10:21] [STDERR] [+0:01:07] 2025-09-18 09:10:21,746 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 3969f110..., 0.8ms)
[2025-09-18 09:10:21] [STDERR] [+0:01:07] 2025-09-18 09:10:21,747 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:21 Cache hit: Steven.Universe.S02E04.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:23] [STDERR] [+0:01:09] 2025-09-18 09:10:23,832 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 29687f16-ea9d-4db4-9cb1-3801b65040c5 -> movie:unknown
[2025-09-18 09:10:23] [STDERR] [+0:01:09] 2025-09-18 09:10:23,832 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 29687f16..., 99.5ms)
[2025-09-18 09:10:23] [STDERR] [+0:01:09] 2025-09-18 09:10:23,832 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 29687f16..., 99.5ms)
[2025-09-18 09:10:23] [STDERR] [+0:01:09] 2025-09-18 09:10:23,832 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:23 Cache hit: Steven.Universe.S02E05.Story.for.Steven.1080p.BluRay.Opus.2.0.x265-edge2020 → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:23] [STDERR] [+0:01:09] 2025-09-18 09:10:23,833 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 352128e7-780f-4b0c-9f3a-8271ee14961a -> movie:unknown
[2025-09-18 09:10:23] [STDERR] [+0:01:09] 2025-09-18 09:10:23,834 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 352128e7..., 1.0ms)
[2025-09-18 09:10:23] [STDERR] [+0:01:09] 2025-09-18 09:10:23,834 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 352128e7..., 1.0ms)
[2025-09-18 09:10:23] [STDERR] [+0:01:09] 2025-09-18 09:10:23,834 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:23 Cache hit: Steven.Universe.S02E05.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:23] [STDERR] [+0:01:09] 2025-09-18 09:10:23,835 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 67a10a70-0510-44f7-b7ea-3e7d1f118397 -> movie:unknown
[2025-09-18 09:10:23] [STDERR] [+0:01:09] 2025-09-18 09:10:23,835 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 67a10a70..., 0.9ms)
[2025-09-18 09:10:23] [STDERR] [+0:01:09] 2025-09-18 09:10:23,835 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 67a10a70..., 0.9ms)
[2025-09-18 09:10:23] [STDERR] [+0:01:09] 2025-09-18 09:10:23,835 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:23 Cache hit: Steven.Universe.S02E05.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:23] [STDERR] [+0:01:09] 2025-09-18 09:10:23,836 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 70f149b1-98b7-478b-849c-a97d76762fb0 -> movie:unknown
[2025-09-18 09:10:23] [STDERR] [+0:01:09] 2025-09-18 09:10:23,836 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 70f149b1..., 0.8ms)
[2025-09-18 09:10:23] [STDERR] [+0:01:09] 2025-09-18 09:10:23,836 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 70f149b1..., 0.8ms)
[2025-09-18 09:10:23] [STDERR] [+0:01:09] 2025-09-18 09:10:23,836 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:23 Cache hit: Steven.Universe.S02E05.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:25] [STDERR] [+0:01:11] 2025-09-18 09:10:25,772 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 3ea576ec-0e9b-418c-aa15-f252152f8817 -> movie:unknown
[2025-09-18 09:10:25] [STDERR] [+0:01:11] 2025-09-18 09:10:25,773 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 3ea576ec..., 47.1ms)
[2025-09-18 09:10:25] [STDERR] [+0:01:11] 2025-09-18 09:10:25,773 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 3ea576ec..., 47.1ms)
[2025-09-18 09:10:25] [STDERR] [+0:01:11] 2025-09-18 09:10:25,774 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:25 Cache hit: Steven.Universe.S02E06.Shirt.Club.1080p.BluRay.Opus.2.0.x265-edge2020 → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:25] [STDERR] [+0:01:11] 2025-09-18 09:10:25,775 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: ad4cf216-af06-4cd3-a586-c845e08dafb2 -> movie:unknown
[2025-09-18 09:10:25] [STDERR] [+0:01:11] 2025-09-18 09:10:25,776 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: ad4cf216..., 1.4ms)
[2025-09-18 09:10:25] [STDERR] [+0:01:11] 2025-09-18 09:10:25,776 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: ad4cf216..., 1.4ms)
[2025-09-18 09:10:25] [STDERR] [+0:01:11] 2025-09-18 09:10:25,776 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:25 Cache hit: Steven.Universe.S02E06.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:25] [STDERR] [+0:01:11] 2025-09-18 09:10:25,777 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: efa9ef74-7784-47aa-8820-aed8481cc37f -> movie:unknown
[2025-09-18 09:10:25] [STDERR] [+0:01:11] 2025-09-18 09:10:25,778 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: efa9ef74..., 1.3ms)
[2025-09-18 09:10:25] [STDERR] [+0:01:11] 2025-09-18 09:10:25,778 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: efa9ef74..., 1.3ms)
[2025-09-18 09:10:25] [STDERR] [+0:01:11] 2025-09-18 09:10:25,778 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:25 Cache hit: Steven.Universe.S02E06.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:25] [STDERR] [+0:01:11] 2025-09-18 09:10:25,780 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: c13f086d-19e9-47f8-adf3-de85d9418a10 -> movie:unknown
[2025-09-18 09:10:25] [STDERR] [+0:01:11] 2025-09-18 09:10:25,780 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: c13f086d..., 1.5ms)
[2025-09-18 09:10:25] [STDERR] [+0:01:11] 2025-09-18 09:10:25,780 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: c13f086d..., 1.5ms)
[2025-09-18 09:10:25] [STDERR] [+0:01:11] 2025-09-18 09:10:25,780 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:25 Cache hit: Steven.Universe.S02E06.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:27] [STDERR] [+0:01:13] 2025-09-18 09:10:27,794 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 04c16d9f-72d3-4dff-849b-09d891d084c1 -> movie:unknown
[2025-09-18 09:10:27] [STDERR] [+0:01:13] 2025-09-18 09:10:27,794 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 04c16d9f..., 1.3ms)
[2025-09-18 09:10:27] [STDERR] [+0:01:13] 2025-09-18 09:10:27,794 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 04c16d9f..., 1.3ms)
[2025-09-18 09:10:27] [STDERR] [+0:01:13] 2025-09-18 09:10:27,794 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:27 Cache hit: Steven.Universe.S02E07.Love.Letters.1080p.BluRay.Opus.2.0.x265-edge2020 → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:27] [STDERR] [+0:01:13] 2025-09-18 09:10:27,795 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 32d47562-6cb4-428b-9c86-1a9848619d86 -> movie:unknown
[2025-09-18 09:10:27] [STDERR] [+0:01:13] 2025-09-18 09:10:27,795 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 32d47562..., 0.9ms)
[2025-09-18 09:10:27] [STDERR] [+0:01:13] 2025-09-18 09:10:27,795 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 32d47562..., 0.9ms)
[2025-09-18 09:10:27] [STDERR] [+0:01:13] 2025-09-18 09:10:27,795 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:27 Cache hit: Steven.Universe.S02E07.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:27] [STDERR] [+0:01:13] 2025-09-18 09:10:27,796 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: c413e06e-0043-4866-9c43-6ea326f77b88 -> movie:unknown
[2025-09-18 09:10:27] [STDERR] [+0:01:13] 2025-09-18 09:10:27,796 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: c413e06e..., 0.7ms)
[2025-09-18 09:10:27] [STDERR] [+0:01:13] 2025-09-18 09:10:27,796 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: c413e06e..., 0.7ms)
[2025-09-18 09:10:27] [STDERR] [+0:01:13] 2025-09-18 09:10:27,796 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:27 Cache hit: Steven.Universe.S02E07.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:27] [STDERR] [+0:01:13] 2025-09-18 09:10:27,797 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 09c5ccb8-7218-4c9b-bf4e-d7a4869c2806 -> movie:unknown
[2025-09-18 09:10:27] [STDERR] [+0:01:13] 2025-09-18 09:10:27,797 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 09c5ccb8..., 0.7ms)
[2025-09-18 09:10:27] [STDERR] [+0:01:13] 2025-09-18 09:10:27,797 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 09c5ccb8..., 0.7ms)
[2025-09-18 09:10:27] [STDERR] [+0:01:13] 2025-09-18 09:10:27,797 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:27 Cache hit: Steven.Universe.S02E07.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:29] [STDERR] [+0:01:15] 2025-09-18 09:10:29,742 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 517f7cd6-83d7-478f-8055-6e38c11ed21b -> movie:unknown
[2025-09-18 09:10:29] [STDERR] [+0:01:15] 2025-09-18 09:10:29,742 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 517f7cd6..., 1.1ms)
[2025-09-18 09:10:29] [STDERR] [+0:01:15] 2025-09-18 09:10:29,742 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 517f7cd6..., 1.1ms)
[2025-09-18 09:10:29] [STDERR] [+0:01:15] 2025-09-18 09:10:29,742 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:29 Cache hit: Steven.Universe.S02E08.Reformed.1080p.BluRay.Opus.2.0.x265-edge2020 → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:29] [STDERR] [+0:01:15] 2025-09-18 09:10:29,743 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 604e49a1-fb75-46df-9b1c-ed86e98ff498 -> movie:unknown
[2025-09-18 09:10:29] [STDERR] [+0:01:15] 2025-09-18 09:10:29,743 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 604e49a1..., 0.9ms)
[2025-09-18 09:10:29] [STDERR] [+0:01:15] 2025-09-18 09:10:29,743 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 604e49a1..., 0.9ms)
[2025-09-18 09:10:29] [STDERR] [+0:01:15] 2025-09-18 09:10:29,743 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:29 Cache hit: Steven.Universe.S02E08.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:29] [STDERR] [+0:01:15] 2025-09-18 09:10:29,744 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 324727a2-cfbe-48a0-a20b-045739c3b803 -> movie:unknown
[2025-09-18 09:10:29] [STDERR] [+0:01:15] 2025-09-18 09:10:29,744 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 324727a2..., 0.7ms)
[2025-09-18 09:10:29] [STDERR] [+0:01:15] 2025-09-18 09:10:29,744 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 324727a2..., 0.7ms)
[2025-09-18 09:10:29] [STDERR] [+0:01:15] 2025-09-18 09:10:29,744 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:29 Cache hit: Steven.Universe.S02E08.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:29] [STDERR] [+0:01:15] 2025-09-18 09:10:29,745 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 79e1b31e-b685-4c9a-8b39-cf2d3fafbecc -> movie:unknown
[2025-09-18 09:10:29] [STDERR] [+0:01:15] 2025-09-18 09:10:29,745 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 79e1b31e..., 0.8ms)
[2025-09-18 09:10:29] [STDERR] [+0:01:15] 2025-09-18 09:10:29,745 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 79e1b31e..., 0.8ms)
[2025-09-18 09:10:29] [STDERR] [+0:01:15] 2025-09-18 09:10:29,745 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:29 Cache hit: Steven.Universe.S02E08.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:31] [STDERR] [+0:01:17] 2025-09-18 09:10:31,834 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 3c7da46e-994a-4b3d-b15f-d1c955f4e3e6 -> movie:unknown
[2025-09-18 09:10:31] [STDERR] [+0:01:17] 2025-09-18 09:10:31,834 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 3c7da46e..., 1.4ms)
[2025-09-18 09:10:31] [STDERR] [+0:01:17] 2025-09-18 09:10:31,834 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 3c7da46e..., 1.4ms)
[2025-09-18 09:10:31] [STDERR] [+0:01:17] 2025-09-18 09:10:31,834 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:31 Cache hit: Steven.Universe.S02E09.Sworn.to.the.Sword.1080p.BluRay.Opus.2.0.x265-edge2020 → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:31] [STDERR] [+0:01:17] 2025-09-18 09:10:31,835 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 10e5cbdc-721d-462d-a8a0-03bfbdd4236a -> movie:unknown
[2025-09-18 09:10:31] [STDERR] [+0:01:17] 2025-09-18 09:10:31,835 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 10e5cbdc..., 0.8ms)
[2025-09-18 09:10:31] [STDERR] [+0:01:17] 2025-09-18 09:10:31,835 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 10e5cbdc..., 0.8ms)
[2025-09-18 09:10:31] [STDERR] [+0:01:17] 2025-09-18 09:10:31,835 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:31 Cache hit: Steven.Universe.S02E09.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:31] [STDERR] [+0:01:17] 2025-09-18 09:10:31,836 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: b625a2be-81b1-4dec-bbff-a38512263b66 -> movie:unknown
[2025-09-18 09:10:31] [STDERR] [+0:01:17] 2025-09-18 09:10:31,836 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: b625a2be..., 0.8ms)
[2025-09-18 09:10:31] [STDERR] [+0:01:17] 2025-09-18 09:10:31,836 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: b625a2be..., 0.8ms)
[2025-09-18 09:10:31] [STDERR] [+0:01:17] 2025-09-18 09:10:31,836 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:31 Cache hit: Steven.Universe.S02E09.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:31] [STDERR] [+0:01:17] 2025-09-18 09:10:31,837 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 7bce1352-49e8-459d-8653-fbe33b3ebe0c -> movie:unknown
[2025-09-18 09:10:31] [STDERR] [+0:01:17] 2025-09-18 09:10:31,837 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 7bce1352..., 0.8ms)
[2025-09-18 09:10:31] [STDERR] [+0:01:17] 2025-09-18 09:10:31,837 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 7bce1352..., 0.8ms)
[2025-09-18 09:10:31] [STDERR] [+0:01:17] 2025-09-18 09:10:31,837 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:31 Cache hit: Steven.Universe.S02E09.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:33] [STDERR] [+0:01:19] 2025-09-18 09:10:33,726 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: aa57892c-e106-4f99-9436-c47821883f75 -> movie:unknown
[2025-09-18 09:10:33] [STDERR] [+0:01:19] 2025-09-18 09:10:33,726 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: aa57892c..., 4.2ms)
[2025-09-18 09:10:33] [STDERR] [+0:01:19] 2025-09-18 09:10:33,726 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: aa57892c..., 4.2ms)
[2025-09-18 09:10:33] [STDERR] [+0:01:19] 2025-09-18 09:10:33,726 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:33 Cache hit: Steven.Universe.S02E10.Rising.Tides.Crashing.Skies.1080p.BluRay.Opus.2.0.x265-edge2020 → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:33] [STDERR] [+0:01:19] 2025-09-18 09:10:33,796 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 42b4ea22-3176-4156-9b36-81ee5440192e -> movie:unknown
[2025-09-18 09:10:33] [STDERR] [+0:01:19] 2025-09-18 09:10:33,796 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 42b4ea22..., 69.7ms)
[2025-09-18 09:10:33] [STDERR] [+0:01:19] 2025-09-18 09:10:33,796 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 42b4ea22..., 69.7ms)
[2025-09-18 09:10:33] [STDERR] [+0:01:19] 2025-09-18 09:10:33,796 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:33 Cache hit: Steven.Universe.S02E10.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:33] [STDERR] [+0:01:19] 2025-09-18 09:10:33,797 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 59785e32-e859-4f4a-8c3c-e4655d93481e -> movie:unknown
[2025-09-18 09:10:33] [STDERR] [+0:01:19] 2025-09-18 09:10:33,797 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 59785e32..., 0.9ms)
[2025-09-18 09:10:33] [STDERR] [+0:01:19] 2025-09-18 09:10:33,797 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 59785e32..., 0.9ms)
[2025-09-18 09:10:33] [STDERR] [+0:01:19] 2025-09-18 09:10:33,797 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:33 Cache hit: Steven.Universe.S02E10.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:33] [STDERR] [+0:01:19] 2025-09-18 09:10:33,798 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 969cadf5-f9a3-49b7-8e0d-bbcd19a390e9 -> movie:unknown
[2025-09-18 09:10:33] [STDERR] [+0:01:19] 2025-09-18 09:10:33,798 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 969cadf5..., 0.9ms)
[2025-09-18 09:10:33] [STDERR] [+0:01:19] 2025-09-18 09:10:33,798 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 969cadf5..., 0.9ms)
[2025-09-18 09:10:33] [STDERR] [+0:01:19] 2025-09-18 09:10:33,798 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:33 Cache hit: Steven.Universe.S02E10.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:35] [STDERR] [+0:01:21] 2025-09-18 09:10:35,748 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 9138c3a4-b8e1-49a0-94f7-ae045ec57f17 -> movie:unknown
[2025-09-18 09:10:35] [STDERR] [+0:01:21] 2025-09-18 09:10:35,748 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9138c3a4..., 1.1ms)
[2025-09-18 09:10:35] [STDERR] [+0:01:21] 2025-09-18 09:10:35,748 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9138c3a4..., 1.1ms)
[2025-09-18 09:10:35] [STDERR] [+0:01:21] 2025-09-18 09:10:35,748 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:35 Cache hit: Steven.Universe.S02E11.Keeping.it.Together.1080p.BluRay.Opus.2.0.x265-edge2020 → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:35] [STDERR] [+0:01:21] 2025-09-18 09:10:35,749 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 98901b80-2606-47c3-9b19-588401aa3bdc -> movie:unknown
[2025-09-18 09:10:35] [STDERR] [+0:01:21] 2025-09-18 09:10:35,749 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 98901b80..., 0.8ms)
[2025-09-18 09:10:35] [STDERR] [+0:01:21] 2025-09-18 09:10:35,749 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 98901b80..., 0.8ms)
[2025-09-18 09:10:35] [STDERR] [+0:01:21] 2025-09-18 09:10:35,750 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:35 Cache hit: Steven.Universe.S02E11.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:35] [STDERR] [+0:01:21] 2025-09-18 09:10:35,750 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: dcc79a28-8e34-4919-abc0-b6f8c3167952 -> movie:unknown
[2025-09-18 09:10:35] [STDERR] [+0:01:21] 2025-09-18 09:10:35,750 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: dcc79a28..., 0.8ms)
[2025-09-18 09:10:35] [STDERR] [+0:01:21] 2025-09-18 09:10:35,750 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: dcc79a28..., 0.8ms)
[2025-09-18 09:10:35] [STDERR] [+0:01:21] 2025-09-18 09:10:35,751 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:35 Cache hit: Steven.Universe.S02E11.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:35] [STDERR] [+0:01:21] 2025-09-18 09:10:35,751 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 9fc8f7e8-2bae-41f0-8964-0e3c342c8ce9 -> movie:unknown
[2025-09-18 09:10:35] [STDERR] [+0:01:21] 2025-09-18 09:10:35,751 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9fc8f7e8..., 0.7ms)
[2025-09-18 09:10:35] [STDERR] [+0:01:21] 2025-09-18 09:10:35,751 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9fc8f7e8..., 0.7ms)
[2025-09-18 09:10:35] [STDERR] [+0:01:21] 2025-09-18 09:10:35,752 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:35 Cache hit: Steven.Universe.S02E11.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:37] [STDERR] [+0:01:23] 2025-09-18 09:10:37,793 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 578a76b8-631d-4a6d-8e4c-bbda059215cd -> movie:unknown
[2025-09-18 09:10:37] [STDERR] [+0:01:23] 2025-09-18 09:10:37,793 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 578a76b8..., 62.4ms)
[2025-09-18 09:10:37] [STDERR] [+0:01:23] 2025-09-18 09:10:37,793 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 578a76b8..., 62.4ms)
[2025-09-18 09:10:37] [STDERR] [+0:01:23] 2025-09-18 09:10:37,793 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:37 Cache hit: Steven.Universe.S02E12.We.Need.to.Talk.1080p.BluRay.Opus.2.0.x265-edge2020 → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:37] [STDERR] [+0:01:23] 2025-09-18 09:10:37,794 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 2e1d075f-6c24-44ee-aba0-aa4f9303306a -> movie:unknown
[2025-09-18 09:10:37] [STDERR] [+0:01:23] 2025-09-18 09:10:37,794 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2e1d075f..., 1.1ms)
[2025-09-18 09:10:37] [STDERR] [+0:01:23] 2025-09-18 09:10:37,794 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2e1d075f..., 1.1ms)
[2025-09-18 09:10:37] [STDERR] [+0:01:23] 2025-09-18 09:10:37,795 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:37 Cache hit: Steven.Universe.S02E12.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:37] [STDERR] [+0:01:23] 2025-09-18 09:10:37,795 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 015dd4de-13b4-4988-b4c0-0964dddc4904 -> movie:unknown
[2025-09-18 09:10:37] [STDERR] [+0:01:23] 2025-09-18 09:10:37,795 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 015dd4de..., 0.7ms)
[2025-09-18 09:10:37] [STDERR] [+0:01:23] 2025-09-18 09:10:37,795 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 015dd4de..., 0.7ms)
[2025-09-18 09:10:37] [STDERR] [+0:01:23] 2025-09-18 09:10:37,795 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:37 Cache hit: Steven.Universe.S02E12.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:37] [STDERR] [+0:01:23] 2025-09-18 09:10:37,796 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 943708eb-2aad-46bd-b5a4-0de07eabf051 -> movie:unknown
[2025-09-18 09:10:37] [STDERR] [+0:01:23] 2025-09-18 09:10:37,796 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 943708eb..., 0.7ms)
[2025-09-18 09:10:37] [STDERR] [+0:01:23] 2025-09-18 09:10:37,796 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 943708eb..., 0.7ms)
[2025-09-18 09:10:37] [STDERR] [+0:01:23] 2025-09-18 09:10:37,796 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:37 Cache hit: Steven.Universe.S02E12.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:39] [STDERR] [+0:01:25] 2025-09-18 09:10:39,803 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 07dcf58a-d93b-4cbd-b7ff-71546d00a5e1 -> movie:unknown
[2025-09-18 09:10:39] [STDERR] [+0:01:25] 2025-09-18 09:10:39,803 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 07dcf58a..., 1.2ms)
[2025-09-18 09:10:39] [STDERR] [+0:01:25] 2025-09-18 09:10:39,803 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 07dcf58a..., 1.2ms)
[2025-09-18 09:10:39] [STDERR] [+0:01:25] 2025-09-18 09:10:39,803 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:39 Cache hit: Steven.Universe.S02E13.Chille.Tid.1080p.BluRay.Opus.2.0.x265-edge2020 → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:39] [STDERR] [+0:01:25] 2025-09-18 09:10:39,804 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: c34479d7-1d15-4701-988b-2951dce507fc -> movie:unknown
[2025-09-18 09:10:39] [STDERR] [+0:01:25] 2025-09-18 09:10:39,804 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: c34479d7..., 0.8ms)
[2025-09-18 09:10:39] [STDERR] [+0:01:25] 2025-09-18 09:10:39,804 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: c34479d7..., 0.8ms)
[2025-09-18 09:10:39] [STDERR] [+0:01:25] 2025-09-18 09:10:39,804 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:39 Cache hit: Steven.Universe.S02E13.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:39] [STDERR] [+0:01:25] 2025-09-18 09:10:39,805 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 1034dc30-a2a1-4539-a1ca-c91fc7ce6c34 -> movie:unknown
[2025-09-18 09:10:39] [STDERR] [+0:01:25] 2025-09-18 09:10:39,805 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 1034dc30..., 0.8ms)
[2025-09-18 09:10:39] [STDERR] [+0:01:25] 2025-09-18 09:10:39,805 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 1034dc30..., 0.8ms)
[2025-09-18 09:10:39] [STDERR] [+0:01:25] 2025-09-18 09:10:39,805 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:39 Cache hit: Steven.Universe.S02E13.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:39] [STDERR] [+0:01:25] 2025-09-18 09:10:39,806 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 3c4697b1-e729-493e-8ada-7f6b1ffcde10 -> movie:unknown
[2025-09-18 09:10:39] [STDERR] [+0:01:25] 2025-09-18 09:10:39,806 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 3c4697b1..., 0.9ms)
[2025-09-18 09:10:39] [STDERR] [+0:01:25] 2025-09-18 09:10:39,806 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 3c4697b1..., 0.9ms)
[2025-09-18 09:10:39] [STDERR] [+0:01:25] 2025-09-18 09:10:39,807 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:39 Cache hit: Steven.Universe.S02E13.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:41] [STDERR] [+0:01:27] 2025-09-18 09:10:41,805 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 1c98aaf2-3562-44ba-a7f6-fe210dd11f9e -> movie:unknown
[2025-09-18 09:10:41] [STDERR] [+0:01:27] 2025-09-18 09:10:41,806 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 1c98aaf2..., 32.4ms)
[2025-09-18 09:10:41] [STDERR] [+0:01:27] 2025-09-18 09:10:41,806 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 1c98aaf2..., 32.4ms)
[2025-09-18 09:10:41] [STDERR] [+0:01:27] 2025-09-18 09:10:41,807 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:41 Cache hit: Steven.Universe.S02E14.Cry.for.Help.1080p.BluRay.Opus.2.0.x265-edge2020 → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:41] [STDERR] [+0:01:27] 2025-09-18 09:10:41,809 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: c1490fb5-ec2d-4af1-a1c5-285cbd034d1d -> movie:unknown
[2025-09-18 09:10:41] [STDERR] [+0:01:27] 2025-09-18 09:10:41,809 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: c1490fb5..., 1.6ms)
[2025-09-18 09:10:41] [STDERR] [+0:01:27] 2025-09-18 09:10:41,809 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: c1490fb5..., 1.6ms)
[2025-09-18 09:10:41] [STDERR] [+0:01:27] 2025-09-18 09:10:41,810 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:41 Cache hit: Steven.Universe.S02E14.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:41] [STDERR] [+0:01:27] 2025-09-18 09:10:41,811 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 37001ae6-84f1-4e3d-bb91-c72ce7edc4c7 -> movie:unknown
[2025-09-18 09:10:41] [STDERR] [+0:01:27] 2025-09-18 09:10:41,811 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 37001ae6..., 1.1ms)
[2025-09-18 09:10:41] [STDERR] [+0:01:27] 2025-09-18 09:10:41,811 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 37001ae6..., 1.1ms)
[2025-09-18 09:10:41] [STDERR] [+0:01:27] 2025-09-18 09:10:41,811 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:41 Cache hit: Steven.Universe.S02E14.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:41] [STDERR] [+0:01:27] 2025-09-18 09:10:41,813 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 8e62833f-1e6e-4dbc-b314-1bbd8fd23c3b -> movie:unknown
[2025-09-18 09:10:41] [STDERR] [+0:01:27] 2025-09-18 09:10:41,813 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 8e62833f..., 1.5ms)
[2025-09-18 09:10:41] [STDERR] [+0:01:27] 2025-09-18 09:10:41,813 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 8e62833f..., 1.5ms)
[2025-09-18 09:10:41] [STDERR] [+0:01:27] 2025-09-18 09:10:41,814 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:41 Cache hit: Steven.Universe.S02E14.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:43] [STDERR] [+0:01:29] 2025-09-18 09:10:43,944 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: c2b74752-fa5c-4e31-996b-4bd2fe78fafd -> movie:unknown
[2025-09-18 09:10:43] [STDERR] [+0:01:29] 2025-09-18 09:10:43,945 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: c2b74752..., 102.6ms)
[2025-09-18 09:10:43] [STDERR] [+0:01:29] 2025-09-18 09:10:43,945 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: c2b74752..., 102.6ms)
[2025-09-18 09:10:43] [STDERR] [+0:01:29] 2025-09-18 09:10:43,945 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:43 Cache hit: Steven.Universe.S02E15.Keystone.Motel.1080p.BluRay.Opus.2.0.x265-edge2020 → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:43] [STDERR] [+0:01:29] 2025-09-18 09:10:43,985 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 64feb902-0fbc-43a9-92b6-ca791d5af9d6 -> movie:unknown
[2025-09-18 09:10:43] [STDERR] [+0:01:29] 2025-09-18 09:10:43,985 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 64feb902..., 40.3ms)
[2025-09-18 09:10:43] [STDERR] [+0:01:29] 2025-09-18 09:10:43,985 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 64feb902..., 40.3ms)
[2025-09-18 09:10:43] [STDERR] [+0:01:29] 2025-09-18 09:10:43,986 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:43 Cache hit: Steven.Universe.S02E15.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:43] [STDERR] [+0:01:29] 2025-09-18 09:10:43,987 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 6b184af7-69fd-42c0-98b1-049da7acce66 -> movie:unknown
[2025-09-18 09:10:43] [STDERR] [+0:01:29] 2025-09-18 09:10:43,987 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 6b184af7..., 1.0ms)
[2025-09-18 09:10:43] [STDERR] [+0:01:29] 2025-09-18 09:10:43,987 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 6b184af7..., 1.0ms)
[2025-09-18 09:10:43] [STDERR] [+0:01:29] 2025-09-18 09:10:43,987 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:43 Cache hit: Steven.Universe.S02E15.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:43] [STDERR] [+0:01:29] 2025-09-18 09:10:43,988 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: aa62b0fe-4d3c-4a39-b042-30250bd53480 -> movie:unknown
[2025-09-18 09:10:43] [STDERR] [+0:01:29] 2025-09-18 09:10:43,988 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: aa62b0fe..., 1.4ms)
[2025-09-18 09:10:43] [STDERR] [+0:01:29] 2025-09-18 09:10:43,988 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: aa62b0fe..., 1.4ms)
[2025-09-18 09:10:43] [STDERR] [+0:01:29] 2025-09-18 09:10:43,989 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:43 Cache hit: Steven.Universe.S02E15.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:45] [STDERR] [+0:01:31] 2025-09-18 09:10:45,788 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 2e31f494-75c2-4b70-834f-9ad51ff789c9 -> movie:unknown
[2025-09-18 09:10:45] [STDERR] [+0:01:31] 2025-09-18 09:10:45,788 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2e31f494..., 1.1ms)
[2025-09-18 09:10:45] [STDERR] [+0:01:31] 2025-09-18 09:10:45,788 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2e31f494..., 1.1ms)
[2025-09-18 09:10:45] [STDERR] [+0:01:31] 2025-09-18 09:10:45,788 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:45 Cache hit: Steven.Universe.S02E16.Onion.Friend.1080p.BluRay.Opus.2.0.x265-edge2020 → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:45] [STDERR] [+0:01:31] 2025-09-18 09:10:45,789 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 79a454b9-d767-4b2a-a6e9-9a2ee70702d7 -> movie:unknown
[2025-09-18 09:10:45] [STDERR] [+0:01:31] 2025-09-18 09:10:45,789 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 79a454b9..., 0.8ms)
[2025-09-18 09:10:45] [STDERR] [+0:01:31] 2025-09-18 09:10:45,789 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 79a454b9..., 0.8ms)
[2025-09-18 09:10:45] [STDERR] [+0:01:31] 2025-09-18 09:10:45,789 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:45 Cache hit: Steven.Universe.S02E16.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:45] [STDERR] [+0:01:31] 2025-09-18 09:10:45,790 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: c9313ff8-b3b7-4704-ae35-e2c9cff93793 -> movie:unknown
[2025-09-18 09:10:45] [STDERR] [+0:01:31] 2025-09-18 09:10:45,790 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: c9313ff8..., 0.7ms)
[2025-09-18 09:10:45] [STDERR] [+0:01:31] 2025-09-18 09:10:45,790 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: c9313ff8..., 0.7ms)
[2025-09-18 09:10:45] [STDERR] [+0:01:31] 2025-09-18 09:10:45,790 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:45 Cache hit: Steven.Universe.S02E16.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:45] [STDERR] [+0:01:31] 2025-09-18 09:10:45,791 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 11abb4fa-e702-4b0b-9fe8-1293b05749e9 -> movie:unknown
[2025-09-18 09:10:45] [STDERR] [+0:01:31] 2025-09-18 09:10:45,791 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 11abb4fa..., 0.8ms)
[2025-09-18 09:10:45] [STDERR] [+0:01:31] 2025-09-18 09:10:45,791 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 11abb4fa..., 0.8ms)
[2025-09-18 09:10:45] [STDERR] [+0:01:31] 2025-09-18 09:10:45,791 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:45 Cache hit: Steven.Universe.S02E16.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:47] [STDERR] [+0:01:33] 2025-09-18 09:10:47,782 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 69b82c62-a7cb-4884-9346-fb8b74bbc298 -> movie:unknown
[2025-09-18 09:10:47] [STDERR] [+0:01:33] 2025-09-18 09:10:47,782 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 69b82c62..., 40.0ms)
[2025-09-18 09:10:47] [STDERR] [+0:01:33] 2025-09-18 09:10:47,782 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 69b82c62..., 40.0ms)
[2025-09-18 09:10:47] [STDERR] [+0:01:33] 2025-09-18 09:10:47,782 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:47 Cache hit: Steven.Universe.S02E17.Historical.Friction.1080p.BluRay.Opus.2.0.x265-edge2020 → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:47] [STDERR] [+0:01:33] 2025-09-18 09:10:47,825 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 2e1fceb2-f278-47d2-a730-66889dcefe67 -> movie:unknown
[2025-09-18 09:10:47] [STDERR] [+0:01:33] 2025-09-18 09:10:47,825 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2e1fceb2..., 42.7ms)
[2025-09-18 09:10:47] [STDERR] [+0:01:33] 2025-09-18 09:10:47,825 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2e1fceb2..., 42.7ms)
[2025-09-18 09:10:47] [STDERR] [+0:01:33] 2025-09-18 09:10:47,826 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:47 Cache hit: Steven.Universe.S02E17.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:47] [STDERR] [+0:01:33] 2025-09-18 09:10:47,827 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 8162b674-7268-4b7a-8e94-5870eba92a5c -> movie:unknown
[2025-09-18 09:10:47] [STDERR] [+0:01:33] 2025-09-18 09:10:47,827 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 8162b674..., 1.0ms)
[2025-09-18 09:10:47] [STDERR] [+0:01:33] 2025-09-18 09:10:47,827 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 8162b674..., 1.0ms)
[2025-09-18 09:10:47] [STDERR] [+0:01:33] 2025-09-18 09:10:47,827 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:47 Cache hit: Steven.Universe.S02E17.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:47] [STDERR] [+0:01:33] 2025-09-18 09:10:47,828 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: d29c7bb8-3df8-4f79-9aa1-e3201631795e -> movie:unknown
[2025-09-18 09:10:47] [STDERR] [+0:01:33] 2025-09-18 09:10:47,828 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: d29c7bb8..., 0.9ms)
[2025-09-18 09:10:47] [STDERR] [+0:01:33] 2025-09-18 09:10:47,828 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: d29c7bb8..., 0.9ms)
[2025-09-18 09:10:47] [STDERR] [+0:01:33] 2025-09-18 09:10:47,828 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:47 Cache hit: Steven.Universe.S02E17.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:49] [STDERR] [+0:01:35] 2025-09-18 09:10:49,773 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 8797153d-080a-42e6-92bc-29553e7268b4 -> movie:unknown
[2025-09-18 09:10:49] [STDERR] [+0:01:35] 2025-09-18 09:10:49,773 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 8797153d..., 1.4ms)
[2025-09-18 09:10:49] [STDERR] [+0:01:35] 2025-09-18 09:10:49,773 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 8797153d..., 1.4ms)
[2025-09-18 09:10:49] [STDERR] [+0:01:35] 2025-09-18 09:10:49,774 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:49 Cache hit: Steven.Universe.S02E18.Friend.Ship.1080p.BluRay.Opus.2.0.x265-edge2020 → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:49] [STDERR] [+0:01:35] 2025-09-18 09:10:49,775 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: b1d23421-25c3-44e9-8106-5726da6c6dad -> movie:unknown
[2025-09-18 09:10:49] [STDERR] [+0:01:35] 2025-09-18 09:10:49,775 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: b1d23421..., 1.1ms)
[2025-09-18 09:10:49] [STDERR] [+0:01:35] 2025-09-18 09:10:49,775 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: b1d23421..., 1.1ms)
[2025-09-18 09:10:49] [STDERR] [+0:01:35] 2025-09-18 09:10:49,775 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:49 Cache hit: Steven.Universe.S02E18.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:49] [STDERR] [+0:01:35] 2025-09-18 09:10:49,776 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 74dc4287-368e-4ada-9820-6f94605405b9 -> movie:unknown
[2025-09-18 09:10:49] [STDERR] [+0:01:35] 2025-09-18 09:10:49,776 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 74dc4287..., 0.8ms)
[2025-09-18 09:10:49] [STDERR] [+0:01:35] 2025-09-18 09:10:49,776 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 74dc4287..., 0.8ms)
[2025-09-18 09:10:49] [STDERR] [+0:01:35] 2025-09-18 09:10:49,776 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:49 Cache hit: Steven.Universe.S02E18.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:49] [STDERR] [+0:01:35] 2025-09-18 09:10:49,777 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: d8196e35-9ca4-402a-8c62-ac13f5f6cb0b -> movie:unknown
[2025-09-18 09:10:49] [STDERR] [+0:01:35] 2025-09-18 09:10:49,777 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: d8196e35..., 0.8ms)
[2025-09-18 09:10:49] [STDERR] [+0:01:35] 2025-09-18 09:10:49,777 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: d8196e35..., 0.8ms)
[2025-09-18 09:10:49] [STDERR] [+0:01:35] 2025-09-18 09:10:49,777 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:49 Cache hit: Steven.Universe.S02E18.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:51] [STDERR] [+0:01:37] 2025-09-18 09:10:51,728 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 40857ce6-a190-4974-9003-35b8db7d85d7 -> movie:unknown
[2025-09-18 09:10:51] [STDERR] [+0:01:37] 2025-09-18 09:10:51,728 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 40857ce6..., 1.1ms)
[2025-09-18 09:10:51] [STDERR] [+0:01:37] 2025-09-18 09:10:51,728 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 40857ce6..., 1.1ms)
[2025-09-18 09:10:51] [STDERR] [+0:01:37] 2025-09-18 09:10:51,729 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:51 Cache hit: Steven.Universe.S02E19.Nightmare.Hospital.1080p.BluRay.Opus.2.0.x265-edge2020 → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:51] [STDERR] [+0:01:37] 2025-09-18 09:10:51,784 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: c89642d0-0598-4844-87dc-ec70becefcaf -> movie:unknown
[2025-09-18 09:10:51] [STDERR] [+0:01:37] 2025-09-18 09:10:51,785 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: c89642d0..., 56.2ms)
[2025-09-18 09:10:51] [STDERR] [+0:01:37] 2025-09-18 09:10:51,785 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: c89642d0..., 56.2ms)
[2025-09-18 09:10:51] [STDERR] [+0:01:37] 2025-09-18 09:10:51,786 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:51 Cache hit: Steven.Universe.S02E19.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:51] [STDERR] [+0:01:37] 2025-09-18 09:10:51,787 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 25f00635-c203-4d6e-9d50-c701a6492d01 -> movie:unknown
[2025-09-18 09:10:51] [STDERR] [+0:01:37] 2025-09-18 09:10:51,788 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 25f00635..., 1.6ms)
[2025-09-18 09:10:51] [STDERR] [+0:01:37] 2025-09-18 09:10:51,788 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 25f00635..., 1.6ms)
[2025-09-18 09:10:51] [STDERR] [+0:01:37] 2025-09-18 09:10:51,788 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:51 Cache hit: Steven.Universe.S02E19.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:51] [STDERR] [+0:01:37] 2025-09-18 09:10:51,789 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 2bf735da-74a2-4eca-a7e3-ff3a4ba0b989 -> movie:unknown
[2025-09-18 09:10:51] [STDERR] [+0:01:37] 2025-09-18 09:10:51,789 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2bf735da..., 1.1ms)
[2025-09-18 09:10:51] [STDERR] [+0:01:37] 2025-09-18 09:10:51,789 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2bf735da..., 1.1ms)
[2025-09-18 09:10:51] [STDERR] [+0:01:37] 2025-09-18 09:10:51,789 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:51 Cache hit: Steven.Universe.S02E19.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:53] [STDERR] [+0:01:39] 2025-09-18 09:10:53,722 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: b586e478-ddf1-41ca-a58c-df5f6bb5ef40 -> movie:unknown
[2025-09-18 09:10:53] [STDERR] [+0:01:39] 2025-09-18 09:10:53,723 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: b586e478..., 18.4ms)
[2025-09-18 09:10:53] [STDERR] [+0:01:39] 2025-09-18 09:10:53,723 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: b586e478..., 18.4ms)
[2025-09-18 09:10:53] [STDERR] [+0:01:39] 2025-09-18 09:10:53,723 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:53 Cache hit: Steven.Universe.S02E20.Sadies.Song.1080p.BluRay.Opus.2.0.x265-edge2020 → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:53] [STDERR] [+0:01:39] 2025-09-18 09:10:53,724 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: f0c6905e-ee0f-4991-8e3c-809e3c22a358 -> movie:unknown
[2025-09-18 09:10:53] [STDERR] [+0:01:39] 2025-09-18 09:10:53,724 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: f0c6905e..., 1.0ms)
[2025-09-18 09:10:53] [STDERR] [+0:01:39] 2025-09-18 09:10:53,724 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: f0c6905e..., 1.0ms)
[2025-09-18 09:10:53] [STDERR] [+0:01:39] 2025-09-18 09:10:53,724 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:53 Cache hit: Steven.Universe.S02E20.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:53] [STDERR] [+0:01:39] 2025-09-18 09:10:53,725 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 9e07eb80-9951-4ee9-b390-d76628ca724f -> movie:unknown
[2025-09-18 09:10:53] [STDERR] [+0:01:39] 2025-09-18 09:10:53,725 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9e07eb80..., 0.9ms)
[2025-09-18 09:10:53] [STDERR] [+0:01:39] 2025-09-18 09:10:53,725 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9e07eb80..., 0.9ms)
[2025-09-18 09:10:53] [STDERR] [+0:01:39] 2025-09-18 09:10:53,725 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:53 Cache hit: Steven.Universe.S02E20.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:53] [STDERR] [+0:01:39] 2025-09-18 09:10:53,726 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 791638a3-688c-4a0a-951c-96c0c829220a -> movie:unknown
[2025-09-18 09:10:53] [STDERR] [+0:01:39] 2025-09-18 09:10:53,726 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 791638a3..., 0.8ms)
[2025-09-18 09:10:53] [STDERR] [+0:01:39] 2025-09-18 09:10:53,726 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 791638a3..., 0.8ms)
[2025-09-18 09:10:53] [STDERR] [+0:01:39] 2025-09-18 09:10:53,726 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:53 Cache hit: Steven.Universe.S02E20.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:55] [STDERR] [+0:01:41] 2025-09-18 09:10:55,727 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: e0ac62a6-b75f-4e57-90fc-9beb9c0fd30d -> movie:unknown
[2025-09-18 09:10:55] [STDERR] [+0:01:41] 2025-09-18 09:10:55,727 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: e0ac62a6..., 1.2ms)
[2025-09-18 09:10:55] [STDERR] [+0:01:41] 2025-09-18 09:10:55,727 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: e0ac62a6..., 1.2ms)
[2025-09-18 09:10:55] [STDERR] [+0:01:41] 2025-09-18 09:10:55,728 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:55 Cache hit: Steven.Universe.S02E21.Catch.and.Release.1080p.BluRay.Opus.2.0.x265-edge2020 → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:55] [STDERR] [+0:01:41] 2025-09-18 09:10:55,729 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 0843e4d5-6c87-4cd8-a8b5-d8e6e13d192f -> movie:unknown
[2025-09-18 09:10:55] [STDERR] [+0:01:41] 2025-09-18 09:10:55,729 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 0843e4d5..., 0.9ms)
[2025-09-18 09:10:55] [STDERR] [+0:01:41] 2025-09-18 09:10:55,729 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 0843e4d5..., 0.9ms)
[2025-09-18 09:10:55] [STDERR] [+0:01:41] 2025-09-18 09:10:55,729 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:55 Cache hit: Steven.Universe.S02E21.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:55] [STDERR] [+0:01:41] 2025-09-18 09:10:55,730 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 9aecca3a-6b91-45ad-adc9-6b0a2011436e -> movie:unknown
[2025-09-18 09:10:55] [STDERR] [+0:01:41] 2025-09-18 09:10:55,730 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9aecca3a..., 0.8ms)
[2025-09-18 09:10:55] [STDERR] [+0:01:41] 2025-09-18 09:10:55,730 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9aecca3a..., 0.8ms)
[2025-09-18 09:10:55] [STDERR] [+0:01:41] 2025-09-18 09:10:55,730 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:55 Cache hit: Steven.Universe.S02E21.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:55] [STDERR] [+0:01:41] 2025-09-18 09:10:55,731 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 4201a5d3-3d2f-4822-8296-9e4122e26a93 -> movie:unknown
[2025-09-18 09:10:55] [STDERR] [+0:01:41] 2025-09-18 09:10:55,731 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 4201a5d3..., 0.8ms)
[2025-09-18 09:10:55] [STDERR] [+0:01:41] 2025-09-18 09:10:55,731 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 4201a5d3..., 0.8ms)
[2025-09-18 09:10:55] [STDERR] [+0:01:41] 2025-09-18 09:10:55,731 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:55 Cache hit: Steven.Universe.S02E21.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:57] [STDERR] [+0:01:43] 2025-09-18 09:10:57,825 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 25d66276-ad37-4e0b-98ae-c7fe8f0c7121 -> movie:unknown
[2025-09-18 09:10:57] [STDERR] [+0:01:43] 2025-09-18 09:10:57,825 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 25d66276..., 6.1ms)
[2025-09-18 09:10:57] [STDERR] [+0:01:43] 2025-09-18 09:10:57,825 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 25d66276..., 6.1ms)
[2025-09-18 09:10:57] [STDERR] [+0:01:43] 2025-09-18 09:10:57,826 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:57 Cache hit: Steven.Universe.S02E22.When.It.Rains.1080p.BluRay.Opus.2.0.x265-edge2020 → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:57] [STDERR] [+0:01:43] 2025-09-18 09:10:57,826 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 05eebe11-d55f-45b4-b59c-a814cbe35b62 -> movie:unknown
[2025-09-18 09:10:57] [STDERR] [+0:01:43] 2025-09-18 09:10:57,827 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 05eebe11..., 0.9ms)
[2025-09-18 09:10:57] [STDERR] [+0:01:43] 2025-09-18 09:10:57,827 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 05eebe11..., 0.9ms)
[2025-09-18 09:10:57] [STDERR] [+0:01:43] 2025-09-18 09:10:57,827 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:57 Cache hit: Steven.Universe.S02E22.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:57] [STDERR] [+0:01:43] 2025-09-18 09:10:57,827 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: b55262a1-6e73-4dbb-a1ae-93394faa94b7 -> movie:unknown
[2025-09-18 09:10:57] [STDERR] [+0:01:43] 2025-09-18 09:10:57,828 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: b55262a1..., 0.7ms)
[2025-09-18 09:10:57] [STDERR] [+0:01:43] 2025-09-18 09:10:57,828 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: b55262a1..., 0.7ms)
[2025-09-18 09:10:57] [STDERR] [+0:01:43] 2025-09-18 09:10:57,828 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:57 Cache hit: Steven.Universe.S02E22.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:57] [STDERR] [+0:01:43] 2025-09-18 09:10:57,828 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 67aacbc8-8ea2-430d-b00e-e0ec769512c9 -> movie:unknown
[2025-09-18 09:10:57] [STDERR] [+0:01:43] 2025-09-18 09:10:57,829 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 67aacbc8..., 0.7ms)
[2025-09-18 09:10:57] [STDERR] [+0:01:43] 2025-09-18 09:10:57,829 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 67aacbc8..., 0.7ms)
[2025-09-18 09:10:57] [STDERR] [+0:01:43] 2025-09-18 09:10:57,829 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:57 Cache hit: Steven.Universe.S02E22.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:59] [STDERR] [+0:01:45] 2025-09-18 09:10:59,836 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 67fe89f6-5989-4182-bf14-7065a9cf3650 -> movie:unknown
[2025-09-18 09:10:59] [STDERR] [+0:01:45] 2025-09-18 09:10:59,836 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 67fe89f6..., 43.6ms)
[2025-09-18 09:10:59] [STDERR] [+0:01:45] 2025-09-18 09:10:59,836 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 67fe89f6..., 43.6ms)
[2025-09-18 09:10:59] [STDERR] [+0:01:45] 2025-09-18 09:10:59,836 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:59 Cache hit: Steven.Universe.S02E23.Back.to.the.Barn.1080p.BluRay.Opus.2.0.x265-edge2020 → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:59] [STDERR] [+0:01:45] 2025-09-18 09:10:59,837 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 4e229560-0e8e-46d7-8498-8dee39bd977c -> movie:unknown
[2025-09-18 09:10:59] [STDERR] [+0:01:45] 2025-09-18 09:10:59,838 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 4e229560..., 1.0ms)
[2025-09-18 09:10:59] [STDERR] [+0:01:45] 2025-09-18 09:10:59,838 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 4e229560..., 1.0ms)
[2025-09-18 09:10:59] [STDERR] [+0:01:45] 2025-09-18 09:10:59,838 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:59 Cache hit: Steven.Universe.S02E23.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:59] [STDERR] [+0:01:45] 2025-09-18 09:10:59,839 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 511d5261-21ef-4062-a9e0-4e1db6dafa7d -> movie:unknown
[2025-09-18 09:10:59] [STDERR] [+0:01:45] 2025-09-18 09:10:59,839 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 511d5261..., 0.7ms)
[2025-09-18 09:10:59] [STDERR] [+0:01:45] 2025-09-18 09:10:59,839 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 511d5261..., 0.7ms)
[2025-09-18 09:10:59] [STDERR] [+0:01:45] 2025-09-18 09:10:59,839 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:59 Cache hit: Steven.Universe.S02E23.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:10:59] [STDERR] [+0:01:45] 2025-09-18 09:10:59,840 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 222f76a8-10b3-45a0-8483-4899d2c4946d -> movie:unknown
[2025-09-18 09:10:59] [STDERR] [+0:01:45] 2025-09-18 09:10:59,840 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 222f76a8..., 0.9ms)
[2025-09-18 09:10:59] [STDERR] [+0:01:45] 2025-09-18 09:10:59,840 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 222f76a8..., 0.9ms)
[2025-09-18 09:10:59] [STDERR] [+0:01:45] 2025-09-18 09:10:59,840 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:10:59 Cache hit: Steven.Universe.S02E23.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:11:01] [STDERR] [+0:01:47] 2025-09-18 09:11:01,793 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: af2750ca-4dc9-4613-a3ba-8ee03d5cb561 -> movie:unknown
[2025-09-18 09:11:01] [STDERR] [+0:01:47] 2025-09-18 09:11:01,794 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: af2750ca..., 1.1ms)
[2025-09-18 09:11:01] [STDERR] [+0:01:47] 2025-09-18 09:11:01,794 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: af2750ca..., 1.1ms)
[2025-09-18 09:11:01] [STDERR] [+0:01:47] 2025-09-18 09:11:01,794 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:11:01 Cache hit: Steven.Universe.S02E24.Too.Far.1080p.BluRay.Opus.2.0.x265-edge2020 → ACCEPT (risk: 0.1322)
[2025-09-18 09:11:01] [STDERR] [+0:01:47] 2025-09-18 09:11:01,795 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 6e396995-e20d-4dbf-ab31-656aa5c56959 -> movie:unknown
[2025-09-18 09:11:01] [STDERR] [+0:01:47] 2025-09-18 09:11:01,795 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 6e396995..., 1.0ms)
[2025-09-18 09:11:01] [STDERR] [+0:01:47] 2025-09-18 09:11:01,795 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 6e396995..., 1.0ms)
[2025-09-18 09:11:01] [STDERR] [+0:01:47] 2025-09-18 09:11:01,795 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:11:01 Cache hit: Steven.Universe.S02E24.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:11:01] [STDERR] [+0:01:47] 2025-09-18 09:11:01,796 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 2c100342-4553-430f-a0ae-cdee7efc6524 -> movie:unknown
[2025-09-18 09:11:01] [STDERR] [+0:01:47] 2025-09-18 09:11:01,796 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2c100342..., 0.8ms)
[2025-09-18 09:11:01] [STDERR] [+0:01:47] 2025-09-18 09:11:01,796 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2c100342..., 0.8ms)
[2025-09-18 09:11:01] [STDERR] [+0:01:47] 2025-09-18 09:11:01,796 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:11:01 Cache hit: Steven.Universe.S02E24.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:11:01] [STDERR] [+0:01:47] 2025-09-18 09:11:01,797 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 2da62b64-3bf0-4092-a3d5-ab68e49e548e -> movie:unknown
[2025-09-18 09:11:01] [STDERR] [+0:01:47] 2025-09-18 09:11:01,797 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2da62b64..., 0.8ms)
[2025-09-18 09:11:01] [STDERR] [+0:01:47] 2025-09-18 09:11:01,797 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2da62b64..., 0.8ms)
[2025-09-18 09:11:01] [STDERR] [+0:01:47] 2025-09-18 09:11:01,797 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:11:01 Cache hit: Steven.Universe.S02E24.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:11:03] [STDERR] [+0:01:49] 2025-09-18 09:11:03,733 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 6f2223b1-6c77-41f9-9e6e-394617e761bf -> movie:unknown
[2025-09-18 09:11:03] [STDERR] [+0:01:49] 2025-09-18 09:11:03,734 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 6f2223b1..., 1.1ms)
[2025-09-18 09:11:03] [STDERR] [+0:01:49] 2025-09-18 09:11:03,734 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 6f2223b1..., 1.1ms)
[2025-09-18 09:11:03] [STDERR] [+0:01:49] 2025-09-18 09:11:03,734 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:11:03 Cache hit: Steven.Universe.S02E25.The.Answer.1080p.BluRay.Opus.2.0.x265-edge2020 → ACCEPT (risk: 0.1322)
[2025-09-18 09:11:03] [STDERR] [+0:01:49] 2025-09-18 09:11:03,735 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: df2ab300-f84e-4838-8983-84a711299538 -> movie:unknown
[2025-09-18 09:11:03] [STDERR] [+0:01:49] 2025-09-18 09:11:03,735 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: df2ab300..., 0.9ms)
[2025-09-18 09:11:03] [STDERR] [+0:01:49] 2025-09-18 09:11:03,735 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: df2ab300..., 0.9ms)
[2025-09-18 09:11:03] [STDERR] [+0:01:49] 2025-09-18 09:11:03,735 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:11:03 Cache hit: Steven.Universe.S02E25.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:11:03] [STDERR] [+0:01:49] 2025-09-18 09:11:03,736 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 3b707c24-342f-4b92-b6b0-d40e00ccbbf5 -> movie:unknown
[2025-09-18 09:11:03] [STDERR] [+0:01:49] 2025-09-18 09:11:03,736 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 3b707c24..., 0.8ms)
[2025-09-18 09:11:03] [STDERR] [+0:01:49] 2025-09-18 09:11:03,736 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 3b707c24..., 0.8ms)
[2025-09-18 09:11:03] [STDERR] [+0:01:49] 2025-09-18 09:11:03,736 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:11:03 Cache hit: Steven.Universe.S02E25.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:11:03] [STDERR] [+0:01:49] 2025-09-18 09:11:03,737 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 26ba16bf-8c33-4d7f-8d28-d641cdb33172 -> movie:unknown
[2025-09-18 09:11:03] [STDERR] [+0:01:49] 2025-09-18 09:11:03,737 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 26ba16bf..., 0.8ms)
[2025-09-18 09:11:03] [STDERR] [+0:01:49] 2025-09-18 09:11:03,737 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 26ba16bf..., 0.8ms)
[2025-09-18 09:11:03] [STDERR] [+0:01:49] 2025-09-18 09:11:03,737 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:11:03 Cache hit: Steven.Universe.S02E25.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:11:05] [STDERR] [+0:01:51] 2025-09-18 09:11:05,717 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: e88445d1-97e7-4e3a-af2c-9dbeb8efc2fe -> movie:unknown
[2025-09-18 09:11:05] [STDERR] [+0:01:51] 2025-09-18 09:11:05,717 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: e88445d1..., 1.1ms)
[2025-09-18 09:11:05] [STDERR] [+0:01:51] 2025-09-18 09:11:05,717 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: e88445d1..., 1.1ms)
[2025-09-18 09:11:05] [STDERR] [+0:01:51] 2025-09-18 09:11:05,717 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:11:05 Cache hit: Steven.Universe.S02E26.Stevens.Birthday.1080p.BluRay.Opus.2.0.x265-edge2020 → ACCEPT (risk: 0.1322)
[2025-09-18 09:11:05] [STDERR] [+0:01:51] 2025-09-18 09:11:05,718 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 30ab68a0-2d7d-47de-8311-7b9138f7aabe -> movie:unknown
[2025-09-18 09:11:05] [STDERR] [+0:01:51] 2025-09-18 09:11:05,718 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 30ab68a0..., 0.8ms)
[2025-09-18 09:11:05] [STDERR] [+0:01:51] 2025-09-18 09:11:05,718 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 30ab68a0..., 0.8ms)
[2025-09-18 09:11:05] [STDERR] [+0:01:51] 2025-09-18 09:11:05,718 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:11:05 Cache hit: Steven.Universe.S02E26.720p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:11:05] [STDERR] [+0:01:51] 2025-09-18 09:11:05,719 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: dd6a022b-e670-4d95-8fcf-7a607a5d1cc1 -> movie:unknown
[2025-09-18 09:11:05] [STDERR] [+0:01:51] 2025-09-18 09:11:05,719 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: dd6a022b..., 0.8ms)
[2025-09-18 09:11:05] [STDERR] [+0:01:51] 2025-09-18 09:11:05,719 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: dd6a022b..., 0.8ms)
[2025-09-18 09:11:05] [STDERR] [+0:01:51] 2025-09-18 09:11:05,719 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:11:05 Cache hit: Steven.Universe.S02E26.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:11:05] [STDERR] [+0:01:51] 2025-09-18 09:11:05,720 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: a2132753-c942-497d-830c-5a3eaa3716f9 -> movie:unknown
[2025-09-18 09:11:05] [STDERR] [+0:01:51] 2025-09-18 09:11:05,720 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: a2132753..., 0.8ms)
[2025-09-18 09:11:05] [STDERR] [+0:01:51] 2025-09-18 09:11:05,720 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: a2132753..., 0.8ms)
[2025-09-18 09:11:05] [STDERR] [+0:01:51] 2025-09-18 09:11:05,720 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:11:05 Cache hit: Steven.Universe.S02E26.1080p.BluRay.x264-TAXES → ACCEPT (risk: 0.1322)
[2025-09-18 09:11:05] [STDERR] [+0:01:51] 2025-09-18 09:11:05,720 - preflight_analyzer.tv_show_preflight_selector - INFO - 🎯 Episode coverage: 29/29 episodes (100.0%)
[2025-09-18 09:11:05] [STDERR] [+0:01:51] 2025-09-18 09:11:05,720 - preflight_analyzer.tv_show_preflight_selector - INFO - 🎯 100% episode coverage - using individual episodes only
[2025-09-18 09:11:05] [STDERR] [+0:01:51] 2025-09-18 09:11:05,721 - preflight_analyzer.tv_show_preflight_selector - INFO - 📝 Final plan: Using 29 individual episodes
[2025-09-18 09:11:05] [STDERR] [+0:01:51] 2025-09-18 09:11:05,721 - preflight_analyzer.tv_show_preflight_selector - INFO - 💾 Cache performance: 106/110 hits (96.4%) - saved significant analysis time!
[2025-09-18 09:11:05] [STDERR] [+0:01:51] 2025-09-18 09:11:05,721 - preflight_analyzer.tv_show_preflight_selector - INFO - ✅ TV preflight analysis completed in 70.12s - Strategy: episodes
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 4 candidates for episode 61539
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S02E01.1080p.BluRay.x264-TAXE... - 1080p, 0.60GB, 🇺🇸, Other, risk: 0.1312
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 2 candidates for episode 61540
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S01E46.1080p.BluRay.x264-TAXE... - 1080p, 0.49GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 4 candidates for episode 61541
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S02E02.1080p.BluRay.x264-TAXE... - 1080p, 0.60GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 4 candidates for episode 61542
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S02E03.1080p.BluRay.x264-TAXE... - 1080p, 0.60GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 2 candidates for episode 61543
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S01E48.1080p.BluRay.x264-TAXE... - 1080p, 0.49GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 2 candidates for episode 61544
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S01E47.1080p.BluRay.x264-TAXE... - 1080p, 0.49GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 4 candidates for episode 61545
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S02E04.1080p.BluRay.x264-TAXE... - 1080p, 0.60GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 4 candidates for episode 61546
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S02E05.1080p.BluRay.x264-TAXE... - 1080p, 0.60GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 4 candidates for episode 61547
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S02E06.1080p.BluRay.x264-TAXE... - 1080p, 0.60GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 4 candidates for episode 61548
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S02E07.1080p.BluRay.x264-TAXE... - 1080p, 0.60GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 4 candidates for episode 61549
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S02E08.1080p.BluRay.x264-TAXE... - 1080p, 0.60GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 4 candidates for episode 61550
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S02E09.1080p.BluRay.x264-TAXE... - 1080p, 0.60GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 4 candidates for episode 61551
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S02E10.1080p.BluRay.x264-TAXE... - 1080p, 0.60GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 4 candidates for episode 61552
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S02E11.1080p.BluRay.x264-TAXE... - 1080p, 0.60GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 4 candidates for episode 61553
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S02E12.1080p.BluRay.x264-TAXE... - 1080p, 0.60GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 4 candidates for episode 61554
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S02E13.1080p.BluRay.x264-TAXE... - 1080p, 0.60GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 4 candidates for episode 61555
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S02E14.1080p.BluRay.x264-TAXE... - 1080p, 0.60GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 4 candidates for episode 61556
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S02E15.1080p.BluRay.x264-TAXE... - 1080p, 0.60GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 4 candidates for episode 61557
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S02E16.1080p.BluRay.x264-TAXE... - 1080p, 0.60GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 4 candidates for episode 61558
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S02E17.1080p.BluRay.x264-TAXE... - 1080p, 0.60GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 4 candidates for episode 61559
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S02E18.1080p.BluRay.x264-TAXE... - 1080p, 0.60GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 4 candidates for episode 61560
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S02E19.1080p.BluRay.x264-TAXE... - 1080p, 0.60GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 4 candidates for episode 61561
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S02E20.1080p.BluRay.x264-TAXE... - 1080p, 0.60GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 4 candidates for episode 61562
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S02E21.1080p.BluRay.x264-TAXE... - 1080p, 0.60GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 4 candidates for episode 61563
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S02E22.1080p.BluRay.x264-TAXE... - 1080p, 0.60GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 4 candidates for episode 61564
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S02E23.1080p.BluRay.x264-TAXE... - 1080p, 0.61GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 4 candidates for episode 61565
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S02E24.1080p.BluRay.x264-TAXE... - 1080p, 0.60GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 4 candidates for episode 61566
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S02E25.1080p.BluRay.x264-TAXE... - 1080p, 0.60GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 DEBUG: Selecting best from 4 candidates for episode 61567
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🔧 NEW LOGIC: Selected Steven.Universe.S02E26.1080p.BluRay.x264-TAXE... - 1080p, 16.15GB, 🇺🇸, Other, risk: 0.1322
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🐛 DEBUG: Filtering 0 accepted packs for season 2
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🐛 DEBUG: Found 0 season-specific packs
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    🐛 DEBUG: No season-specific packs found, season_pack = None
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51]    ➤ Season 2: 29/29 episodes covered (100.00%) with 110 files, strategy=episodes
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 🚀 Starting downloads for Season 2 immediately...
[2025-09-18 09:11:05] [STDOUT] [+0:01:51] 
[2025-09-18 09:11:06] [STDOUT] [+0:01:52]    ✅ Started download: Steven.Universe.S02E01.1080p.BluRay.x264-TAXES
[2025-09-18 09:11:06] [STDOUT] [+0:01:52] 
[2025-09-18 09:11:08] [STDOUT] [+0:01:53]    ✅ Started download: Steven.Universe.S01E46.1080p.BluRay.x264-TAXES
[2025-09-18 09:11:08] [STDOUT] [+0:01:53] 
[2025-09-18 09:11:10] [STDOUT] [+0:01:56]    ✅ Started download: Steven.Universe.S02E02.1080p.BluRay.x264-TAXES
[2025-09-18 09:11:10] [STDOUT] [+0:01:56] 
[2025-09-18 09:11:12] [STDOUT] [+0:01:57]    ✅ Started download: Steven.Universe.S02E03.1080p.BluRay.x264-TAXES
[2025-09-18 09:11:12] [STDOUT] [+0:01:57] 
[2025-09-18 09:11:14] [STDOUT] [+0:01:59]    ✅ Started download: Steven.Universe.S01E48.1080p.BluRay.x264-TAXES
[2025-09-18 09:11:14] [STDOUT] [+0:01:59] 
[2025-09-18 09:11:16] [STDOUT] [+0:02:01]    ✅ Started download: Steven.Universe.S01E47.1080p.BluRay.x264-TAXES
[2025-09-18 09:11:16] [STDOUT] [+0:02:01] 
[2025-09-18 09:11:18] [STDOUT] [+0:02:03]    ✅ Started download: Steven.Universe.S02E04.1080p.BluRay.x264-TAXES
[2025-09-18 09:11:18] [STDOUT] [+0:02:03] 
[2025-09-18 09:11:20] [STDOUT] [+0:02:06]    ✅ Started download: Steven.Universe.S02E05.1080p.BluRay.x264-TAXES
[2025-09-18 09:11:20] [STDOUT] [+0:02:06] 
[2025-09-18 09:11:22] [STDOUT] [+0:02:07]    ✅ Started download: Steven.Universe.S02E06.1080p.BluRay.x264-TAXES
[2025-09-18 09:11:22] [STDOUT] [+0:02:07] 
[2025-09-18 09:11:24] [STDOUT] [+0:02:10]    ✅ Started download: Steven.Universe.S02E07.1080p.BluRay.x264-TAXES
[2025-09-18 09:11:24] [STDOUT] [+0:02:10] 
[2025-09-18 09:11:28] [STDOUT] [+0:02:13]    ✅ Started download: Steven.Universe.S02E08.1080p.BluRay.x264-TAXES
[2025-09-18 09:11:28] [STDOUT] [+0:02:13] 
[2025-09-18 09:11:30] [STDOUT] [+0:02:15]    ✅ Started download: Steven.Universe.S02E09.1080p.BluRay.x264-TAXES
[2025-09-18 09:11:30] [STDOUT] [+0:02:15] 
[2025-09-18 09:11:32] [STDOUT] [+0:02:17]    ✅ Started download: Steven.Universe.S02E10.1080p.BluRay.x264-TAXES
[2025-09-18 09:11:32] [STDOUT] [+0:02:17] 
[2025-09-18 09:11:34] [STDOUT] [+0:02:19]    ✅ Started download: Steven.Universe.S02E11.1080p.BluRay.x264-TAXES
[2025-09-18 09:11:34] [STDOUT] [+0:02:19] 
[2025-09-18 09:11:36] [STDOUT] [+0:02:21]    ✅ Started download: Steven.Universe.S02E12.1080p.BluRay.x264-TAXES
[2025-09-18 09:11:36] [STDOUT] [+0:02:21] 
[2025-09-18 09:11:38] [STDOUT] [+0:02:23]    ✅ Started download: Steven.Universe.S02E13.1080p.BluRay.x264-TAXES
[2025-09-18 09:11:38] [STDOUT] [+0:02:23] 
[2025-09-18 09:11:40] [STDOUT] [+0:02:26]    ✅ Started download: Steven.Universe.S02E14.1080p.BluRay.x264-TAXES
[2025-09-18 09:11:40] [STDOUT] [+0:02:26] 
[2025-09-18 09:11:42] [STDOUT] [+0:02:28]    ✅ Started download: Steven.Universe.S02E15.1080p.BluRay.x264-TAXES
[2025-09-18 09:11:42] [STDOUT] [+0:02:28] 
[2025-09-18 09:11:44] [STDOUT] [+0:02:30]    ✅ Started download: Steven.Universe.S02E16.1080p.BluRay.x264-TAXES
[2025-09-18 09:11:44] [STDOUT] [+0:02:30] 
[2025-09-18 09:11:46] [STDOUT] [+0:02:32]    ✅ Started download: Steven.Universe.S02E17.1080p.BluRay.x264-TAXES
[2025-09-18 09:11:46] [STDOUT] [+0:02:32] 
[2025-09-18 09:11:48] [STDOUT] [+0:02:34]    ✅ Started download: Steven.Universe.S02E18.1080p.BluRay.x264-TAXES
[2025-09-18 09:11:48] [STDOUT] [+0:02:34] 
[2025-09-18 09:11:52] [STDOUT] [+0:02:38]    ✅ Started download: Steven.Universe.S02E19.1080p.BluRay.x264-TAXES
[2025-09-18 09:11:52] [STDOUT] [+0:02:38] 
[2025-09-18 09:11:56] [STDOUT] [+0:02:42]    ✅ Started download: Steven.Universe.S02E20.1080p.BluRay.x264-TAXES
[2025-09-18 09:11:56] [STDOUT] [+0:02:42] 
[2025-09-18 09:11:58] [STDOUT] [+0:02:44]    ✅ Started download: Steven.Universe.S02E21.1080p.BluRay.x264-TAXES
[2025-09-18 09:11:58] [STDOUT] [+0:02:44] 
[2025-09-18 09:12:00] [STDOUT] [+0:02:46]    ✅ Started download: Steven.Universe.S02E22.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:00] [STDOUT] [+0:02:46] 
[2025-09-18 09:12:02] [STDOUT] [+0:02:48]    ✅ Started download: Steven.Universe.S02E23.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:02] [STDOUT] [+0:02:48] 
[2025-09-18 09:12:04] [STDOUT] [+0:02:50]    ✅ Started download: Steven.Universe.S02E24.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:04] [STDOUT] [+0:02:50] 
[2025-09-18 09:12:06] [STDOUT] [+0:02:52]    ✅ Started download: Steven.Universe.S02E25.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:06] [STDOUT] [+0:02:52] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    ✅ Started download: Steven.Universe.S02E26.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    🎯 Season 2: 29 downloads started
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 📊 Combined Results: 29 total episodes analyzed, 29 acceptable episodes + 0 season packs found
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 📊 Queue Status: 29 items queued across 1 season(s) (telemetry tracking enabled)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 📝 Season 2 decision saved: workspace\preflight_decisions\tv_shows\Steven_Universe_S02.json
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] ✅ Preflight found and started downloads for 29 episodes + 0 packs across 1 season(s)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 🔬 Preflight Episode Selections (already downloading):
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #1. 📺 Steven.Universe.S02E01.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.60 GB (641,532,015 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1312 | Missing: 0.0% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #2. 📺 Steven.Universe.S01E46.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.49 GB (528,814,039 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #3. 📺 Steven.Universe.S02E02.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.60 GB (640,417,235 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #4. 📺 Steven.Universe.S02E03.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.60 GB (640,476,952 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #5. 📺 Steven.Universe.S01E48.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.49 GB (528,704,935 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #6. 📺 Steven.Universe.S01E47.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.49 GB (528,192,945 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #7. 📺 Steven.Universe.S02E04.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.60 GB (641,052,646 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #8. 📺 Steven.Universe.S02E05.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.60 GB (639,988,076 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #9. 📺 Steven.Universe.S02E06.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.60 GB (640,533,384 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #10. 📺 Steven.Universe.S02E07.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.60 GB (640,018,085 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #11. 📺 Steven.Universe.S02E08.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.60 GB (641,984,746 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #12. 📺 Steven.Universe.S02E09.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.60 GB (642,270,431 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #13. 📺 Steven.Universe.S02E10.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.60 GB (640,555,035 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #14. 📺 Steven.Universe.S02E11.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.60 GB (641,210,183 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #15. 📺 Steven.Universe.S02E12.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.60 GB (646,325,802 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #16. 📺 Steven.Universe.S02E13.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.60 GB (644,758,460 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #17. 📺 Steven.Universe.S02E14.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.60 GB (641,057,547 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #18. 📺 Steven.Universe.S02E15.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.60 GB (641,130,673 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #19. 📺 Steven.Universe.S02E16.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.60 GB (641,260,067 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #20. 📺 Steven.Universe.S02E17.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.60 GB (641,053,468 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #21. 📺 Steven.Universe.S02E18.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.60 GB (642,114,556 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #22. 📺 Steven.Universe.S02E19.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.60 GB (641,070,808 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #23. 📺 Steven.Universe.S02E20.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.60 GB (639,384,398 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #24. 📺 Steven.Universe.S02E21.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.60 GB (644,453,711 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #25. 📺 Steven.Universe.S02E22.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.60 GB (640,524,972 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #26. 📺 Steven.Universe.S02E23.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.61 GB (655,874,985 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #27. 📺 Steven.Universe.S02E24.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.60 GB (642,081,470 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #28. 📺 Steven.Universe.S02E25.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 0.60 GB (641,179,576 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    #29. 📺 Steven.Universe.S02E26.1080p.BluRay.x264-TAXES
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        💾 Size: 16.15 GB (17,338,582,670 bytes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 📊 Preflight Summary: 29 episodes + 0 packs | Total: 32.57 GB
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 🎯 Downloads started immediately after each season analysis
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    (No waiting for all analysis to complete - optimal efficiency!)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDERR] [+0:02:55] 2025-09-18 09:12:09,656 - interactive_pipeline_01 - WARNING - Preflight skipped: missing Sonarr series id
[2025-09-18 09:12:09] [STDERR] [+0:02:55] 2025-09-18 09:12:09,659 - interactive_pipeline_01 - INFO - Stored enhanced TV metadata for: Steven Universe (2013)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 📍 Progress: 2/2
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 📺 Processing: Futurama (1999) S01
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    🎯 Request Type: Specific Season
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDOUT] [+0:02:55]    📀 Target: S01 (all episodes)
[2025-09-18 09:12:09] [STDOUT] [+0:02:55] 
[2025-09-18 09:12:09] [STDERR] [+0:02:55] 2025-09-18 09:12:09,660 - interactive_pipeline_01 - INFO - Processing TV show: Futurama (1999) S01 (specific_season)
[2025-09-18 09:12:09] [STDERR] [+0:02:55] 2025-09-18 09:12:09,819 - _internal.src.metadata_fetcher - INFO - TMDb TV search with year 1999: 1 results
[2025-09-18 09:12:10] [STDERR] [+0:02:56] 2025-09-18 09:12:10,433 - _internal.utils.fuzzy_matching - INFO - Found 1 exact matches for 'Futurama', prioritizing them
[2025-09-18 09:12:11] [STDERR] [+0:02:57] 2025-09-18 09:12:11,650 - _internal.src.metadata_fetcher - ERROR - Error during TV fuzzy matching for 'Futurama': EnhancedFuzzyMatchingConfig.get_year_tolerance() missing 1 required positional argument: 'content_type'
[2025-09-18 09:12:11] [STDOUT] [+0:02:57] ✅ Found metadata: Futurama (1999)
[2025-09-18 09:12:11] [STDOUT] [+0:02:57] 
[2025-09-18 09:12:11] [STDERR] [+0:02:57] 2025-09-18 09:12:11,695 - interactive_pipeline_01 - INFO - Successfully found TV metadata for: Futurama
[2025-09-18 09:12:11] [STDOUT] [+0:02:57] 🔍 Fetching complete episode data from TVDB...
[2025-09-18 09:12:11] [STDOUT] [+0:02:57] 
[2025-09-18 09:12:11] [STDERR] [+0:02:57] 2025-09-18 09:12:11,696 - interactive_pipeline_01 - INFO - Fetching TVDB episode data for: Futurama
[2025-09-18 09:12:12] [STDOUT] [+0:02:58] 🔍 DEBUG: Data source: TMDb TV API (fallback from TVDB)
[2025-09-18 09:12:12] [STDOUT] [+0:02:58] 
[2025-09-18 09:12:12] [STDOUT] [+0:02:58] 🔍 DEBUG: Total seasons reported: 6
[2025-09-18 09:12:12] [STDOUT] [+0:02:58] 
[2025-09-18 09:12:12] [STDOUT] [+0:02:58] 🔍 DEBUG: Total episodes reported: 125
[2025-09-18 09:12:12] [STDOUT] [+0:02:58] 
[2025-09-18 09:12:12] [STDOUT] [+0:02:58] 🔍 DEBUG: Season 1: 25 episodes
[2025-09-18 09:12:12] [STDOUT] [+0:02:58] 
[2025-09-18 09:12:12] [STDOUT] [+0:02:58] 🔍 DEBUG: Season 1 episodes: ['Episode 1', 'Episode 2', 'Episode 3', 'Episode 4', 'Episode 5']
[2025-09-18 09:12:12] [STDOUT] [+0:02:58] 
[2025-09-18 09:12:12] [STDOUT] [+0:02:58] 🔍 DEBUG: Season 2: 24 episodes
[2025-09-18 09:12:12] [STDOUT] [+0:02:58] 
[2025-09-18 09:12:12] [STDOUT] [+0:02:58] 🔍 DEBUG: Season 3: 25 episodes
[2025-09-18 09:12:12] [STDOUT] [+0:02:58] 
[2025-09-18 09:12:12] [STDOUT] [+0:02:58] 🔍 DEBUG: Season 4: 23 episodes
[2025-09-18 09:12:12] [STDOUT] [+0:02:58] 
[2025-09-18 09:12:12] [STDOUT] [+0:02:58] 🔍 DEBUG: Season 5: 24 episodes
[2025-09-18 09:12:12] [STDOUT] [+0:02:58] 
[2025-09-18 09:12:12] [STDOUT] [+0:02:58] 🔍 DEBUG: Season 6: 4 episodes
[2025-09-18 09:12:12] [STDOUT] [+0:02:58] 
[2025-09-18 09:12:12] [STDOUT] [+0:02:58] ✅ Found complete series data: 6 seasons, 125 episodes
[2025-09-18 09:12:12] [STDOUT] [+0:02:58] 
[2025-09-18 09:12:12] [STDERR] [+0:02:58] 2025-09-18 09:12:12,423 - interactive_pipeline_01 - INFO - TVDB data retrieved: 6 seasons, 125 episodes
[2025-09-18 09:12:12] [STDERR] [+0:02:58] 2025-09-18 09:12:12,423 - interactive_pipeline_01 - INFO - Created season queue: 25 episodes from seasons [1]
[2025-09-18 09:12:12] [STDOUT] [+0:02:58] 📋 Created chronological download queue: 25 episodes
[2025-09-18 09:12:12] [STDOUT] [+0:02:58] 
[2025-09-18 09:12:12] [STDOUT] [+0:02:58]    📺 First 3 episodes: S01E01, S01E02, S01E03...
[2025-09-18 09:12:12] [STDOUT] [+0:02:58] 
[2025-09-18 09:12:12] [STDOUT] [+0:02:58]    📺 Last episode: S01E25
[2025-09-18 09:12:12] [STDOUT] [+0:02:58] 
[2025-09-18 09:12:12] [STDERR] [+0:02:58] 2025-09-18 09:12:12,424 - interactive_pipeline_01 - INFO - 🧪 Skipping creation of season pack blocking profile (preflight handles pack decisions)
[2025-09-18 09:12:12] [STDERR] [+0:02:58] 2025-09-18 09:12:12,425 - interactive_pipeline_01 - INFO - 📺 TV Quality Strategy (ADAPTIVE): Adaptive Quality: Using inclusive profile (ID 6) - prevents episode skipping, allows best available quality selection
[2025-09-18 09:12:12] [STDERR] [+0:02:58] 2025-09-18 09:12:12,425 - interactive_pipeline_01 - INFO - 📺 Year-based preference hint: 1999 (used for internal logic, not restrictions)
[2025-09-18 09:12:12] [STDERR] [+0:02:58] 2025-09-18 09:12:12,425 - interactive_pipeline_01 - INFO - Searching Sonarr for: Futurama 1999
[2025-09-18 09:12:14] [STDERR] [+0:02:59] 2025-09-18 09:12:14,074 - interactive_pipeline_01 - INFO - Selected TV show: Futurama (1999) | tvdbId=73871 | alternatives: [{'title': 'Family Feud (1999)', 'year': 1999, 'tvdbId': 335567, 'score': 378}, {'title': 'Kaj og Andrea (1999)', 'year': 1999, 'tvdbId': 263611, 'score': 330}, {'title': 'Bad Girls (1999)', 'year': 1999, 'tvdbId': 75328, 'score': 327}]
[2025-09-18 09:12:14] [STDERR] [+0:02:59] 2025-09-18 09:12:14,079 - interactive_pipeline_01 - INFO - 📁 Matched existing Sonarr root folder: E:\
[2025-09-18 09:12:14] [STDERR] [+0:02:59] 2025-09-18 09:12:14,079 - interactive_pipeline_01 - INFO - 📋 Adaptive Quality: Using inclusive profile (ID 6) - prevents episode skipping, allows best available quality selection
[2025-09-18 09:12:14] [STDERR] [+0:02:59] 2025-09-18 09:12:14,079 - interactive_pipeline_01 - INFO - 📺 Adding TV show to Sonarr with 1 quality profile(s): Futurama
[2025-09-18 09:12:14] [STDERR] [+0:02:59] 2025-09-18 09:12:14,083 - interactive_pipeline_01 - INFO - 🔁 Series already exists in Sonarr (ID 368); applying specificity adjustments.
[2025-09-18 09:12:14] [STDERR] [+0:02:59] 2025-09-18 09:12:14,098 - interactive_pipeline_01 - INFO -    🎯 Configured monitoring for Season None
[2025-09-18 09:12:14] [STDERR] [+0:02:59] 2025-09-18 09:12:14,102 - interactive_pipeline_01 - WARNING -    ⚠️ No episodes found for Season None
[2025-09-18 09:12:19] [STDERR] [+0:03:04] 2025-09-18 09:12:19,118 - interactive_pipeline_01 - INFO - Episode size enforcement: no oversized items detected
[2025-09-18 09:12:19] [STDOUT] [+0:03:04]    📀 Configured for: S01 (all episodes)
[2025-09-18 09:12:19] [STDOUT] [+0:03:04] 
[2025-09-18 09:12:19] [STDOUT] [+0:03:04]    📋 Queued 25 episodes for chronological download
[2025-09-18 09:12:19] [STDOUT] [+0:03:04] 
[2025-09-18 09:12:19] [STDOUT] [+0:03:04] 
[2025-09-18 09:12:19] [STDOUT] [+0:03:04] 🔍 Alternative candidate matches (top scoring):
[2025-09-18 09:12:19] [STDOUT] [+0:03:04] 
[2025-09-18 09:12:19] [STDOUT] [+0:03:04]    • Family Feud (1999) (1999) tvdb:335567 score:378
[2025-09-18 09:12:19] [STDOUT] [+0:03:04] 
[2025-09-18 09:12:19] [STDOUT] [+0:03:04]    • Kaj og Andrea (1999) (1999) tvdb:263611 score:330
[2025-09-18 09:12:19] [STDOUT] [+0:03:04] 
[2025-09-18 09:12:19] [STDOUT] [+0:03:04]    • Bad Girls (1999) (1999) tvdb:75328 score:327
[2025-09-18 09:12:19] [STDOUT] [+0:03:04] 
[2025-09-18 09:12:19] [STDERR] [+0:03:04] 2025-09-18 09:12:19,119 - interactive_pipeline_01 - INFO - ✅ Successfully added TV show to Sonarr: Futurama
[2025-09-18 09:12:19] [STDOUT] [+0:03:04] 🤖 Auto Mode: Using Preflight Analysis for Futurama
[2025-09-18 09:12:19] [STDOUT] [+0:03:04] 
[2025-09-18 09:12:19] [STDOUT] [+0:03:04] 🔬 Using Preflight Analysis for Futurama
[2025-09-18 09:12:19] [STDOUT] [+0:03:04] 
[2025-09-18 09:12:19] [STDERR] [+0:03:04] 2025-09-18 09:12:19,120 - interactive_pipeline_01 - INFO - User selected preflight analysis for: Futurama
[2025-09-18 09:12:19] [STDOUT] [+0:03:04] 🔎 Preflight analyzing 1 season(s): [1]
[2025-09-18 09:12:19] [STDOUT] [+0:03:04] 
[2025-09-18 09:12:19] [STDOUT] [+0:03:04]    Season 1: All episodes
[2025-09-18 09:12:19] [STDOUT] [+0:03:04] 
[2025-09-18 09:12:27] [STDOUT] [+0:03:12] 
[2025-09-18 09:12:27] [STDOUT] [+0:03:12] 🎯 Analyzing Season 1...
[2025-09-18 09:12:27] [STDOUT] [+0:03:12] 
[2025-09-18 09:12:27] [STDOUT] [+0:03:12] 🆕 No existing decision found for Season 1, running fresh analysis
[2025-09-18 09:12:27] [STDOUT] [+0:03:12] 
[2025-09-18 09:12:27] [STDERR] [+0:03:12] 2025-09-18 09:12:27,129 - preflight_analyzer.tv_show_preflight_selector - INFO - 🎬 Starting TV preflight analysis - Series: 368, Episodes: 9, Mode: standard
[2025-09-18 09:12:27] [STDERR] [+0:03:12] 2025-09-18 09:12:27,129 - preflight_analyzer.tv_show_preflight_selector - INFO - 📋 Analysis configuration - Cache: True, Deduplicate: True, Fresh checks: False
[2025-09-18 09:12:27] [STDERR] [+0:03:12] 2025-09-18 09:12:27,129 - preflight_analyzer.cache_observability - INFO - Initialized enhanced cache metrics with max_events=10000
[2025-09-18 09:12:27] [STDERR] [+0:03:12] 2025-09-18 09:12:27,129 - preflight_analyzer.memory_cache - INFO - Initialized memory cache with maxsize=1000, ttl=43200s
[2025-09-18 09:12:27] [STDERR] [+0:03:12] 2025-09-18 09:12:27,135 - preflight_analyzer.persistent_cache - INFO - Initialized persistent cache at workspace\preflight_cache\cache\analysis_cache.db
[2025-09-18 09:12:27] [STDERR] [+0:03:12] 2025-09-18 09:12:27,135 - preflight_analyzer.guid_reconciler - INFO - Initialized GUID reconciler with size_tolerance=0.05, title_threshold=0.8, min_confidence=0.7, groups=True, indexers=True
[2025-09-18 09:12:27] [STDERR] [+0:03:12] 2025-09-18 09:12:27,135 - preflight_analyzer.multi_layer_cache - INFO - Initialized multi-layer cache at workspace\preflight_cache\cache
[2025-09-18 09:12:27] [STDERR] [+0:03:12] 2025-09-18 09:12:27,135 - preflight_analyzer.cache - INFO - Initialized decision cache at workspace\preflight_cache\cache
[2025-09-18 09:12:27] [STDERR] [+0:03:12] 2025-09-18 09:12:27,172 - preflight_analyzer.tv_show_preflight_selector - INFO - 📺 Analyzing 9 episodes from Futurama Season 1
[2025-09-18 09:12:27] [STDERR] [+0:03:12] 2025-09-18 09:12:27,172 - preflight_analyzer.tv_show_preflight_selector - INFO - 📺 Analyzing 9 episodes in parallel (max 6 concurrent)
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,844 - preflight_analyzer.multi_layer_cache - INFO - L2 content key reconciliation: be276fe2-d61e-4858-9313-4cfa24647e8e -> movie:unknown
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,845 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: be276fe2..., 2.4ms)
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,845 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: be276fe2..., 2.4ms)
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,845 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:27 Cache hit: Futurama - S01E01 - Space Pilot 3000 - WEBDL-720p → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,846 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 89a18b04-d8df-4fae-b387-bb6949b0917f -> movie:unknown
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,846 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 89a18b04..., 0.9ms)
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,846 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 89a18b04..., 0.9ms)
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,846 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:27 Cache hit: Futurama - S01E01 - Space Pilot 3000 - WEBDL-720p → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,847 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: b721b110-1f53-4a96-a8ac-9a8f0f93ad20 -> movie:unknown
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,847 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: b721b110..., 0.8ms)
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,847 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: b721b110..., 0.8ms)
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,847 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:27 Cache hit: Futurama.S01E01.Space.Pilot.3000.1080p.Hulu.WEB-DL.AAC2.0.H265-HighTimes → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,848 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 2882feed-f644-4a3f-9d1e-b9e9db51c6e4 -> movie:unknown
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,848 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2882feed..., 0.8ms)
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,848 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2882feed..., 0.8ms)
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,848 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:27 Cache hit: Futurama.1999.S01E01.Zeit.und.Raum.3000.GERMAN.DL.720p.WEB.H264-TSCC → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,849 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 3b05bebb-5dbb-4dff-80c1-1a3cce059973 -> movie:unknown
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,849 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 3b05bebb..., 0.9ms)
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,849 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 3b05bebb..., 0.9ms)
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,849 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:27 Cache hit: Futurama.S01E01.Space.Pilot.3000.720p.DSNP.WEB-DL.AAC2.0.H.264-playWEB → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,850 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 2498bd03-4af7-42ae-ad3a-3e1516c02050 -> movie:unknown
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,850 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2498bd03..., 0.8ms)
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,850 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2498bd03..., 0.8ms)
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,850 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:27 Cache hit: Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,851 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 9a8609a2-c17b-4160-8780-24e025b3e269 -> movie:unknown
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,851 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9a8609a2..., 0.8ms)
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,851 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9a8609a2..., 0.8ms)
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,851 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:27 Cache hit: Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,852 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 91cd5e8f-b86a-4d87-aa96-eff7b8b951da -> movie:unknown
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,852 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 91cd5e8f..., 0.8ms)
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,852 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 91cd5e8f..., 0.8ms)
[2025-09-18 09:12:27] [STDERR] [+0:03:13] 2025-09-18 09:12:27,852 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:27 Cache hit: Futurama.S01E01.Zeit.und.Raum.3000.GERMAN.DL.FS.1080p.WEB.H264-CNHD → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,549 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 069ca167-6eda-4403-8fea-f3b775c0ac4d -> movie:unknown
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,549 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 069ca167..., 1.6ms)
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,549 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 069ca167..., 1.6ms)
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,549 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:29 Cache hit: Futurama - S01E02 - The Series Has Landed - WEBDL-720p → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,550 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 15c94bf8-c098-4a12-b977-ba22605b1bc6 -> movie:unknown
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,550 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 15c94bf8..., 0.8ms)
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,550 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 15c94bf8..., 0.8ms)
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,550 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:29 Cache hit: Futurama.S01E02.The.Series.Has.Landed.1080p.Hulu.WEB-DL.AAC2.0.H265-HighTimes → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,551 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 877c2982-b632-4b61-b65c-adc886832a97 -> movie:unknown
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,551 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 877c2982..., 0.9ms)
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,551 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 877c2982..., 0.9ms)
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,551 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:29 Cache hit: Futurama.S01E02.The.Series.Has.Landed.AAC2.0.1080p.WEBRip.x265-SiQ → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,552 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 0f5d6940-9595-4ad6-87b3-6b06671702eb -> movie:unknown
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,552 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 0f5d6940..., 1.0ms)
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,552 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 0f5d6940..., 1.0ms)
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,553 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:29 Cache hit: Futurama.1999.S01E02.Sein.erster.Flug.zum.Mond.GERMAN.DL.720p.WEB.H264-TSCC → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,554 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 6935fe2d-f51f-4155-ad4d-75db1d3495c8 -> movie:unknown
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,554 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 6935fe2d..., 1.0ms)
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,554 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 6935fe2d..., 1.0ms)
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,554 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:29 Cache hit: Futurama.S01E02.The.Series.Has.Landed.720p.DSNP.WEB-DL.AAC2.0.H.264-playWEB → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,555 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 88b18145-c21d-4bee-b5ff-7c8d143c8c02 -> movie:unknown
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,555 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 88b18145..., 0.9ms)
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,555 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 88b18145..., 0.9ms)
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,555 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:29 Cache hit: Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,556 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: d29ec0d7-0671-448d-9e08-a8ab5a85b191 -> movie:unknown
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,556 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: d29ec0d7..., 0.8ms)
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,556 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: d29ec0d7..., 0.8ms)
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,556 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:29 Cache hit: Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,557 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: ada044c0-6bf9-4363-bf0f-ec4418a3f314 -> movie:unknown
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,557 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: ada044c0..., 0.8ms)
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,557 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: ada044c0..., 0.8ms)
[2025-09-18 09:12:29] [STDERR] [+0:03:15] 2025-09-18 09:12:29,557 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:29 Cache hit: Futurama.S01E02.Sein.erster.Flug.zum.Mond.GERMAN.DL.FS.1080p.WEB.H264-CNHD → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,536 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: e8fbe176-4774-4284-bfd4-046525a83794 -> movie:unknown
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,536 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: e8fbe176..., 1.4ms)
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,536 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: e8fbe176..., 1.4ms)
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,536 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:31 Cache hit: Futurama - S01E03 - I, Roommate - WEBDL-720p → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,537 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: df383dda-6607-4e7e-b423-b99ade5b01f8 -> movie:unknown
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,537 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: df383dda..., 0.9ms)
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,537 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: df383dda..., 0.9ms)
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,537 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:31 Cache hit: Futurama.S01E03.I,.Roommate.1080p.Hulu.WEB-DL.AAC2.0.H265-HighTimes → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,538 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 9dff30ba-d428-4974-bc41-ba12b6aa7f2a -> movie:unknown
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,538 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9dff30ba..., 0.8ms)
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,538 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9dff30ba..., 0.8ms)
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,538 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:31 Cache hit: Futurama.S01E03.I.Roommate.AAC2.0.1080p.WEBRip.x265-SiQ → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,539 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 17e4bb0a-2f02-4e94-a514-a9001707e5a4 -> movie:unknown
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,539 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 17e4bb0a..., 0.8ms)
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,539 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 17e4bb0a..., 0.8ms)
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,539 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:31 Cache hit: Futurama.1999.S01E03.Wohnungssuche.in.Neu.New.York.GERMAN.DL.720p.WEB.H264-TSCC → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,540 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: af6bcb74-4f7b-4825-ba94-ab795f7dac1e -> movie:unknown
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,540 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: af6bcb74..., 0.8ms)
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,540 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: af6bcb74..., 0.8ms)
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,540 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:31 Cache hit: Futurama.S01E03.I.Roommate.720p.DSNP.WEB-DL.AAC2.0.H.264-playWEB → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,541 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: a1847867-15cd-45bc-9719-fec62c5feea4 -> movie:unknown
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,541 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: a1847867..., 0.9ms)
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,541 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: a1847867..., 0.9ms)
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,542 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:31 Cache hit: Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,543 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 3eb8af2d-06ca-4177-93d0-e57ec39c91b6 -> movie:unknown
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,543 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 3eb8af2d..., 0.9ms)
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,543 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 3eb8af2d..., 0.9ms)
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,543 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:31 Cache hit: Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,544 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 156ff052-a4ba-4da2-9cc8-2235a2e5c304 -> movie:unknown
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,544 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 156ff052..., 0.9ms)
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,544 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 156ff052..., 0.9ms)
[2025-09-18 09:12:31] [STDERR] [+0:03:17] 2025-09-18 09:12:31,544 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:31 Cache hit: Futurama.S01E03.Wohnungssuche.in.Neu.New.York.GERMAN.DL.FS.1080p.WEB.H264-CNHD → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,794 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: ecf1f208-eb92-49f0-8b55-a446a4b01aee -> movie:unknown
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,795 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: ecf1f208..., 1.2ms)
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,795 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: ecf1f208..., 1.2ms)
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,795 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:33 Cache hit: Futurama - S01E04 - Love's Labors Lost in Space - WEBDL-720p → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,796 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: e850386f-9cc0-4673-807a-c0311ef43920 -> movie:unknown
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,796 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: e850386f..., 0.9ms)
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,796 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: e850386f..., 0.9ms)
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,796 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:33 Cache hit: Futurama - S01E04 - Love's Labors Lost in Space - WEBDL-720p → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,797 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 1ad9994a-8be2-4128-96df-783c217c6968 -> movie:unknown
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,797 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 1ad9994a..., 1.0ms)
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,797 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 1ad9994a..., 1.0ms)
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,797 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:33 Cache hit: Futurama.S01E04.Loves.Labors.Lost.in.Space.AAC2.0.1080p.WEBRip.x265-SiQ → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,798 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: afebaec7-4005-4d03-8e35-cfbac0ad8738 -> movie:unknown
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,798 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: afebaec7..., 0.8ms)
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,798 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: afebaec7..., 0.8ms)
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,798 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:33 Cache hit: Futurama.S01E04.Loves.Labours.Lost.in.Space.720p.DSNP.WEB-DL.AAC2.0.H.264-playWEB → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,799 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 8aaf5363-f3e1-4535-ac3d-7ec974175184 -> movie:unknown
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,799 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 8aaf5363..., 0.8ms)
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,799 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 8aaf5363..., 0.8ms)
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,799 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:33 Cache hit: Futurama.1999.S01E04.Begegnung.mit.Zapp.Brannigan.GERMAN.DL.720p.WEB.H264-TSCC → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,800 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 56d8d0a2-dd01-4bb7-91a7-c865acf8da18 -> movie:unknown
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,800 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 56d8d0a2..., 0.8ms)
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,800 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 56d8d0a2..., 0.8ms)
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,800 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:33 Cache hit: Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,801 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: eb59f1b1-7419-44ab-b6ee-49a5e20d00f1 -> movie:unknown
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,801 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: eb59f1b1..., 0.9ms)
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,801 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: eb59f1b1..., 0.9ms)
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,801 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:33 Cache hit: Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,802 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 4e01b215-d7bb-4170-bdb3-a5dbf80da36f -> movie:unknown
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,802 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 4e01b215..., 0.9ms)
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,802 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 4e01b215..., 0.9ms)
[2025-09-18 09:12:33] [STDERR] [+0:03:19] 2025-09-18 09:12:33,802 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:33 Cache hit: Futurama.S01E04.Begegnung.mit.Zapp.Brannigan.GERMAN.DL.FS.1080p.WEB.H264-CNHD → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,523 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 7a60f4b6-3a41-4f70-8782-05d717935018 -> movie:unknown
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,523 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 7a60f4b6..., 1.1ms)
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,523 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 7a60f4b6..., 1.1ms)
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,523 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:35 Cache hit: Futurama - S01E05 - Fear of a Bot Planet - WEBDL-720p → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,524 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 9b838388-4ddb-47eb-937c-25c62f96091b -> movie:unknown
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,524 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9b838388..., 0.8ms)
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,524 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9b838388..., 0.8ms)
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,524 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:35 Cache hit: Futurama.S01E05.Fear.of.a.Bot.Planet.AAC2.0.1080p.WEBRip.x265-SiQ → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,525 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 73e82275-da1b-4ab3-951c-b97d1efb020c -> movie:unknown
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,525 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 73e82275..., 0.7ms)
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,525 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 73e82275..., 0.7ms)
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,525 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:35 Cache hit: Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.Hulu.WEB-DL.AAC2.0.H265-HighTimes → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,526 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 8109b46d-7134-4dda-9747-594df25fce30 -> movie:unknown
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,526 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 8109b46d..., 0.8ms)
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,526 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 8109b46d..., 0.8ms)
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,526 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:35 Cache hit: Futurama.1999.S01E05.Planet.der.Roboter.GERMAN.DL.720p.WEB.H264-TSCC → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,527 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 73cdf473-22ca-48f4-8035-05aa9ed64999 -> movie:unknown
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,527 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 73cdf473..., 0.8ms)
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,527 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 73cdf473..., 0.8ms)
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,527 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:35 Cache hit: Futurama.S01E05.Fear.of.a.Bot.Planet.720p.DSNP.WEB-DL.AAC2.0.H.264-playWEB → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,528 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 8286e7f6-4db3-4e6b-a182-c6c816ee5cd2 -> movie:unknown
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,528 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 8286e7f6..., 0.8ms)
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,528 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 8286e7f6..., 0.8ms)
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,528 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:35 Cache hit: Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,529 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 9103b7bf-2224-4aec-ab84-2420555d8362 -> movie:unknown
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,530 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9103b7bf..., 1.0ms)
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,530 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9103b7bf..., 1.0ms)
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,530 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:35 Cache hit: Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,531 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: e10b51b8-a108-4129-bf67-aaf2d6a2df3a -> movie:unknown
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,531 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: e10b51b8..., 0.9ms)
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,531 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: e10b51b8..., 0.9ms)
[2025-09-18 09:12:35] [STDERR] [+0:03:21] 2025-09-18 09:12:35,531 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:35 Cache hit: Futurama.S01E05.Planet.der.Roboter.GERMAN.DL.FS.1080p.WEB.H264-CNHD → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,522 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 9655738e-bb18-4863-b960-56690cf460ab -> movie:unknown
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,522 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9655738e..., 1.4ms)
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,522 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 9655738e..., 1.4ms)
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,522 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:37 Cache hit: Futurama - S01E06 - A Fishful of Dollars - WEBDL-720p → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,526 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 13bebb21-09d1-4e6e-95fd-689840a814c0 -> movie:unknown
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,526 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 13bebb21..., 3.5ms)
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,526 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 13bebb21..., 3.5ms)
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,526 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:37 Cache hit: Futurama.S01E06.A.Fishful.of.Dollars.AAC2.0.1080p.WEBRip.x265-SiQ → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,527 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: c3e16f65-66d5-4185-8615-989c7aefaf32 -> movie:unknown
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,527 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: c3e16f65..., 1.0ms)
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,527 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: c3e16f65..., 1.0ms)
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,527 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:37 Cache hit: Futurama.S01E06.A.Fishful.of.Dollars.1080p.Hulu.WEB-DL.AAC2.0.H265-HighTimes → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,528 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 55ba1eb2-c082-4593-ac20-ab2ac26d8828 -> movie:unknown
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,528 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 55ba1eb2..., 0.9ms)
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,528 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 55ba1eb2..., 0.9ms)
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,529 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:37 Cache hit: Futurama.1999.S01E06.Das.Geheimnis.der.Anchovis.GERMAN.DL.720p.WEB.H264-TSCC → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,530 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 47fed23e-b41c-4895-9af6-1d01b53bc51a -> movie:unknown
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,530 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 47fed23e..., 0.8ms)
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,530 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 47fed23e..., 0.8ms)
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,530 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:37 Cache hit: Futurama.S01E06.A.Fishful.of.Dollars.720p.DSNP.WEB-DL.AAC2.0.H.264-playWEB → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,531 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: def6dd58-aab5-4a99-bc0c-809f1b99ccc7 -> movie:unknown
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,531 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: def6dd58..., 0.8ms)
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,531 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: def6dd58..., 0.8ms)
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,531 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:37 Cache hit: Futurama.S01E06.Das.Geheimnis.der.Anchovis.GERMAN.DL.FS.1080p.WEB.H264-CNHD → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,532 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: bcca56bd-3078-4040-aa02-6b2a3d96bf8f -> movie:unknown
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,532 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: bcca56bd..., 0.7ms)
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,532 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: bcca56bd..., 0.7ms)
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,532 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:37 Cache hit: Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,533 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: bf81081c-1f40-459f-a388-ebb018c77346 -> movie:unknown
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,533 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: bf81081c..., 1.2ms)
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,533 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: bf81081c..., 1.2ms)
[2025-09-18 09:12:37] [STDERR] [+0:03:23] 2025-09-18 09:12:37,533 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:37 Cache hit: Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,551 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: f7c14746-00ce-488d-9bec-6f354536b8e3 -> movie:unknown
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,552 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: f7c14746..., 2.5ms)
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,552 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: f7c14746..., 2.5ms)
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,552 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:39 Cache hit: Futurama - S01E07 - My Three Suns - WEBDL-720p → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,555 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 1155ca5b-8131-4e66-8a07-489c1a19a684 -> movie:unknown
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,555 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 1155ca5b..., 2.6ms)
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,555 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 1155ca5b..., 2.6ms)
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,555 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:39 Cache hit: Futurama.S01E07.My.Three.Suns.1080p.Hulu.WEB-DL.AAC2.0.H265-HighTimes → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,556 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 0f04459a-b17f-46d8-b4f8-dad994725f66 -> movie:unknown
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,557 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 0f04459a..., 1.5ms)
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,557 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 0f04459a..., 1.5ms)
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,557 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:39 Cache hit: Futurama.S01E07.My.Three.Suns.AAC2.0.1080p.WEBRip.x265-SiQ → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,557 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 68a3dd87-a536-4b53-853f-22b991b4e1bd -> movie:unknown
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,558 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 68a3dd87..., 0.8ms)
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,558 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 68a3dd87..., 0.8ms)
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,558 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:39 Cache hit: Futurama.1999.S01E07.Die.Galaxis.des.Terrors.GERMAN.DL.720p.WEB.H264-TSCC → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,559 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 2cb1513e-9b43-4aeb-b8d3-c361d817f32f -> movie:unknown
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,559 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2cb1513e..., 0.8ms)
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,559 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2cb1513e..., 0.8ms)
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,559 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:39 Cache hit: Futurama.S01E07.My.Three.Suns.720p.DSNP.WEB-DL.AAC2.0.H.264-playWEB → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,560 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 126894c5-c42d-4c47-877b-f53b30b91255 -> movie:unknown
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,560 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 126894c5..., 0.7ms)
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,560 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 126894c5..., 0.7ms)
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,560 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:39 Cache hit: Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,560 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 2f8b99e9-3554-48f9-91be-d86ce8de34c3 -> movie:unknown
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,561 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2f8b99e9..., 0.7ms)
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,561 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 2f8b99e9..., 0.7ms)
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,561 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:39 Cache hit: Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,561 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: ecd26de1-2bb4-4d91-9eb6-b796d54a4ae4 -> movie:unknown
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,562 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: ecd26de1..., 0.8ms)
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,562 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: ecd26de1..., 0.8ms)
[2025-09-18 09:12:39] [STDERR] [+0:03:25] 2025-09-18 09:12:39,562 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:39 Cache hit: Futurama.S01E07.Die.Galaxis.des.Terrors.GERMAN.DL.FS.1080p.WEB.H264-CNHD → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,566 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 01a85ded-82ed-4801-a7bb-ea4dfe3e1e93 -> movie:unknown
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,566 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 01a85ded..., 1.1ms)
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,566 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 01a85ded..., 1.1ms)
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,566 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:41 Cache hit: Futurama - S01E08 - A Big Piece of Garbage - WEBDL-720p → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,567 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: f513faaf-606d-492f-8fc6-b7a8944eee65 -> movie:unknown
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,567 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: f513faaf..., 0.9ms)
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,567 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: f513faaf..., 0.9ms)
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,567 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:41 Cache hit: Futurama.S01E08.A.Big.Piece.of.Garbage.AAC2.0.1080p.WEBRip.x265-SiQ → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,568 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: a558dbfd-e8fc-46f3-90a6-ca31f6b40f80 -> movie:unknown
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,568 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: a558dbfd..., 0.8ms)
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,568 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: a558dbfd..., 0.8ms)
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,568 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:41 Cache hit: Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.Hulu.WEB-DL.AAC2.0.H265-HighTimes → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,569 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 30862028-56f6-4a6d-8abb-6f07ad9ea89a -> movie:unknown
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,569 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 30862028..., 0.9ms)
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,569 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 30862028..., 0.9ms)
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,569 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:41 Cache hit: Futurama.1999.S01E08.Muell.macht.erfinderisch.GERMAN.DL.720p.WEB.H264-TSCC → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,570 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 4189190b-78b7-407d-8714-79e89ef1162a -> movie:unknown
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,570 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 4189190b..., 0.9ms)
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,570 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 4189190b..., 0.9ms)
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,570 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:41 Cache hit: Futurama.S01E08.A.Big.Piece.of.Garbage.720p.DSNP.WEB-DL.AAC2.0.H.264-playWEB → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,571 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: fdd743af-9fa3-4b3f-a154-2c9af290864d -> movie:unknown
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,571 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: fdd743af..., 0.7ms)
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,571 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: fdd743af..., 0.7ms)
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,571 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:41 Cache hit: Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,572 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: *************-4599-a9a5-0b74d3a10e1e -> movie:unknown
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,572 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 59453754..., 0.7ms)
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,572 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 59453754..., 0.7ms)
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,572 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:41 Cache hit: Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,573 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 822cfb46-cec7-4a6d-bcf2-94d813441443 -> movie:unknown
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,573 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 822cfb46..., 0.7ms)
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,573 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 822cfb46..., 0.7ms)
[2025-09-18 09:12:41] [STDERR] [+0:03:27] 2025-09-18 09:12:41,573 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:41 Cache hit: Futurama.S01E08.Muell.macht.erfinderisch.GERMAN.DL.FS.1080p.WEB.H264-CNHD → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,534 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: be07f93f-4770-4ac1-ab8a-4cac9e8ef682 -> movie:unknown
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,534 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: be07f93f..., 1.5ms)
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,534 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: be07f93f..., 1.5ms)
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,534 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:43 Cache hit: Futurama.S01E09.Hell.is.Other.Robots.AAC2.0.1080p.WEBRip.x265-SiQ → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,535 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: e82931b3-e4b5-4b89-af95-bec621d1019a -> movie:unknown
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,535 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: e82931b3..., 1.0ms)
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,535 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: e82931b3..., 1.0ms)
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,536 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:43 Cache hit: Futurama.S01E09.Hell.Is.Other.Robots.1080p.Hulu.WEB-DL.AAC2.0.H265-HighTimes → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,536 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 607be730-10f9-4526-a3e2-e558bd5452d1 -> movie:unknown
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,537 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 607be730..., 0.8ms)
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,537 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 607be730..., 0.8ms)
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,537 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:43 Cache hit: Futurama.1999.S01E09.Ein.echtes.Hoellenspektakel.GERMAN.DL.720p.WEB.H264-TSCC → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,537 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 872a6311-058c-4582-81c4-4640c15dad2e -> movie:unknown
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,538 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 872a6311..., 0.8ms)
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,538 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 872a6311..., 0.8ms)
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,538 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:43 Cache hit: Futurama.S01E09.Hell.Is.Other.Robots.720p.DSNP.WEB-DL.AAC2.0.H.264-playWEB → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,539 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: b75e4cdf-46e2-4b2e-bc9e-e1c98747df7b -> movie:unknown
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,539 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: b75e4cdf..., 0.9ms)
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,539 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: b75e4cdf..., 0.9ms)
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,539 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:43 Cache hit: Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,540 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 5ee95a06-9219-4da9-a85c-9716f51880d0 -> movie:unknown
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,540 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 5ee95a06..., 0.9ms)
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,540 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 5ee95a06..., 0.9ms)
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,540 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:43 Cache hit: Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,541 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 21a6e354-3f4e-47c2-a156-8136306984fe -> movie:unknown
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,541 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 21a6e354..., 0.9ms)
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,541 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 21a6e354..., 0.9ms)
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,541 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:43 Cache hit: Futurama.S01E09.Ein.echtes.Hoellenspektakel.GERMAN.DL.FS.1080p.WEB.H264-CNHD → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,542 - preflight_analyzer.multi_layer_cache - INFO - L1 content key reconciliation: 4a1bcee7-faa9-4b25-8de2-e17d3cc5a64f -> movie:unknown
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,542 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 4a1bcee7..., 0.7ms)
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,542 - cache_operations - INFO - 💾 cache_api cache hit: movie:unknown (GUID: 4a1bcee7..., 0.7ms)
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,542 - preflight_analyzer.tv_show_preflight_selector - INFO -    💾 09:12:43 Cache hit: Futurama.S01E09.1080p.WEBRip.X265-RARBG → ACCEPT (risk: 0.1322)
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,543 - preflight_analyzer.tv_show_preflight_selector - INFO - 🎯 Episode coverage: 9/9 episodes (100.0%)
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,543 - preflight_analyzer.tv_show_preflight_selector - INFO - 🎯 100% episode coverage - using individual episodes only
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,543 - preflight_analyzer.tv_show_preflight_selector - INFO - 📝 Final plan: Using 9 individual episodes
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,543 - preflight_analyzer.tv_show_preflight_selector - INFO - 💾 Cache performance: 72/72 hits (100.0%) - saved significant analysis time!
[2025-09-18 09:12:43] [STDERR] [+0:03:29] 2025-09-18 09:12:43,543 - preflight_analyzer.tv_show_preflight_selector - INFO - ✅ TV preflight analysis completed in 16.41s - Strategy: episodes
[2025-09-18 09:12:43] [STDOUT] [+0:03:29]    🔧 DEBUG: Selecting best from 8 candidates for episode 61322
[2025-09-18 09:12:43] [STDOUT] [+0:03:29] 
[2025-09-18 09:12:43] [STDOUT] [+0:03:29]    🔧 NEW LOGIC: Selected Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.W... - 1080p, 0.94GB, 🇺🇸, Yassmiso, risk: 0.1322
[2025-09-18 09:12:43] [STDOUT] [+0:03:29] 
[2025-09-18 09:12:43] [STDOUT] [+0:03:29]    🔧 DEBUG: Selecting best from 8 candidates for episode 61323
[2025-09-18 09:12:43] [STDOUT] [+0:03:29] 
[2025-09-18 09:12:43] [STDOUT] [+0:03:29]    🔧 NEW LOGIC: Selected Futurama.S01E02.The.Series.Has.Landed.1080p.D... - 1080p, 1.00GB, 🇺🇸, Yassmiso, risk: 0.1322
[2025-09-18 09:12:43] [STDOUT] [+0:03:29] 
[2025-09-18 09:12:43] [STDOUT] [+0:03:29]    🔧 DEBUG: Selecting best from 8 candidates for episode 61324
[2025-09-18 09:12:43] [STDOUT] [+0:03:29] 
[2025-09-18 09:12:43] [STDOUT] [+0:03:29]    🔧 NEW LOGIC: Selected Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.... - 1080p, 0.95GB, 🇺🇸, Yassmiso, risk: 0.1322
[2025-09-18 09:12:43] [STDOUT] [+0:03:29] 
[2025-09-18 09:12:43] [STDOUT] [+0:03:29]    🔧 DEBUG: Selecting best from 8 candidates for episode 61325
[2025-09-18 09:12:43] [STDOUT] [+0:03:29] 
[2025-09-18 09:12:43] [STDOUT] [+0:03:29]    🔧 NEW LOGIC: Selected Futurama.S01E04.Loves.Labours.Lost.in.Space.1... - 1080p, 0.95GB, 🇺🇸, Yassmiso, risk: 0.1322
[2025-09-18 09:12:43] [STDOUT] [+0:03:29] 
[2025-09-18 09:12:43] [STDOUT] [+0:03:29]    🔧 DEBUG: Selecting best from 8 candidates for episode 61326
[2025-09-18 09:12:43] [STDOUT] [+0:03:29] 
[2025-09-18 09:12:43] [STDOUT] [+0:03:29]    🔧 NEW LOGIC: Selected Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DS... - 1080p, 1.07GB, 🇺🇸, Yassmiso, risk: 0.1322
[2025-09-18 09:12:43] [STDOUT] [+0:03:29] 
[2025-09-18 09:12:43] [STDOUT] [+0:03:29]    🔧 DEBUG: Selecting best from 8 candidates for episode 61327
[2025-09-18 09:12:43] [STDOUT] [+0:03:29] 
[2025-09-18 09:12:43] [STDOUT] [+0:03:29]    🔧 NEW LOGIC: Selected Futurama.S01E06.A.Fishful.of.Dollars.1080p.DS... - 1080p, 1.13GB, 🇺🇸, Yassmiso, risk: 0.1322
[2025-09-18 09:12:43] [STDOUT] [+0:03:29] 
[2025-09-18 09:12:43] [STDOUT] [+0:03:29]    🔧 DEBUG: Selecting best from 8 candidates for episode 61328
[2025-09-18 09:12:43] [STDOUT] [+0:03:29] 
[2025-09-18 09:12:43] [STDOUT] [+0:03:29]    🔧 NEW LOGIC: Selected Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-... - 1080p, 0.99GB, 🇺🇸, Yassmiso, risk: 0.1322
[2025-09-18 09:12:43] [STDOUT] [+0:03:29] 
[2025-09-18 09:12:43] [STDOUT] [+0:03:29]    🔧 DEBUG: Selecting best from 8 candidates for episode 61329
[2025-09-18 09:12:43] [STDOUT] [+0:03:29] 
[2025-09-18 09:12:43] [STDOUT] [+0:03:29]    🔧 NEW LOGIC: Selected Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.... - 1080p, 1.07GB, 🇺🇸, Yassmiso, risk: 0.1322
[2025-09-18 09:12:43] [STDOUT] [+0:03:29] 
[2025-09-18 09:12:43] [STDOUT] [+0:03:29]    🔧 DEBUG: Selecting best from 8 candidates for episode 61330
[2025-09-18 09:12:43] [STDOUT] [+0:03:29] 
[2025-09-18 09:12:43] [STDOUT] [+0:03:29]    🔧 NEW LOGIC: Selected Futurama.S01E09.Hell.Is.Other.Robots.1080p.DS... - 1080p, 1.16GB, 🇺🇸, Yassmiso, risk: 0.1322
[2025-09-18 09:12:43] [STDOUT] [+0:03:29] 
[2025-09-18 09:12:43] [STDOUT] [+0:03:29]    🐛 DEBUG: Filtering 0 accepted packs for season 1
[2025-09-18 09:12:43] [STDOUT] [+0:03:29] 
[2025-09-18 09:12:43] [STDOUT] [+0:03:29]    🐛 DEBUG: Found 0 season-specific packs
[2025-09-18 09:12:43] [STDOUT] [+0:03:29] 
[2025-09-18 09:12:43] [STDOUT] [+0:03:29]    🐛 DEBUG: No season-specific packs found, season_pack = None
[2025-09-18 09:12:43] [STDOUT] [+0:03:29] 
[2025-09-18 09:12:43] [STDOUT] [+0:03:29]    ➤ Season 1: 9/9 episodes covered (100.00%) with 72 files, strategy=episodes
[2025-09-18 09:12:43] [STDOUT] [+0:03:29] 
[2025-09-18 09:12:43] [STDOUT] [+0:03:29] 🚀 Starting downloads for Season 1 immediately...
[2025-09-18 09:12:43] [STDOUT] [+0:03:29] 
[2025-09-18 09:12:44] [STDOUT] [+0:03:29]    ✅ Started download: Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 09:12:44] [STDOUT] [+0:03:29] 
[2025-09-18 09:12:46] [STDOUT] [+0:03:31]    ✅ Started download: Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 09:12:46] [STDOUT] [+0:03:31] 
[2025-09-18 09:12:48] [STDOUT] [+0:03:33]    ✅ Started download: Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 09:12:48] [STDOUT] [+0:03:33] 
[2025-09-18 09:12:50] [STDOUT] [+0:03:35]    ✅ Started download: Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 09:12:50] [STDOUT] [+0:03:35] 
[2025-09-18 09:12:52] [STDOUT] [+0:03:37]    ✅ Started download: Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 09:12:52] [STDOUT] [+0:03:37] 
[2025-09-18 09:12:54] [STDOUT] [+0:03:39]    ✅ Started download: Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 09:12:54] [STDOUT] [+0:03:39] 
[2025-09-18 09:12:56] [STDOUT] [+0:03:41]    ✅ Started download: Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 09:12:56] [STDOUT] [+0:03:41] 
[2025-09-18 09:12:58] [STDOUT] [+0:03:43]    ✅ Started download: Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 09:12:58] [STDOUT] [+0:03:43] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]    ✅ Started download: Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]    🎯 Season 1: 9 downloads started
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 📊 Combined Results: 9 total episodes analyzed, 9 acceptable episodes + 0 season packs found
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 📊 Queue Status: 9 items queued across 1 season(s) (telemetry tracking enabled)
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 📝 Season 1 decision saved: workspace\preflight_decisions\tv_shows\Futurama_S01.json
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] ✅ Preflight found and started downloads for 9 episodes + 0 packs across 1 season(s)
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 🔬 Preflight Episode Selections (already downloading):
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]    #1. 📺 Futurama.S01E01.Space.Pilot.3000.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]        💾 Size: 0.94 GB (1,007,222,772 bytes)
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]    #2. 📺 Futurama.S01E02.The.Series.Has.Landed.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]        💾 Size: 1.00 GB (1,077,680,182 bytes)
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]    #3. 📺 Futurama.S01E03.I.Roommate.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]        💾 Size: 0.95 GB (1,024,063,524 bytes)
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]    #4. 📺 Futurama.S01E04.Loves.Labours.Lost.in.Space.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]        💾 Size: 0.95 GB (1,019,816,776 bytes)
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]    #5. 📺 Futurama.S01E05.Fear.of.a.Bot.Planet.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]        💾 Size: 1.07 GB (1,145,373,943 bytes)
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]    #6. 📺 Futurama.S01E06.A.Fishful.of.Dollars.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]        💾 Size: 1.13 GB (1,211,715,074 bytes)
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]    #7. 📺 Futurama.S01E07.My.Three.Suns.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]        💾 Size: 0.99 GB (1,058,658,854 bytes)
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]    #8. 📺 Futurama.S01E08.A.Big.Piece.of.Garbage.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]        💾 Size: 1.07 GB (1,153,156,503 bytes)
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]    #9. 📺 Futurama.S01E09.Hell.Is.Other.Robots.1080p.DSNP.WEB-DL.AAC2.0.H.264-Yassmiso
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]        💾 Size: 1.16 GB (1,248,163,517 bytes)
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]        ⚡ Risk: 0.1322 | Missing: 0.4% | Decision: ACCEPT
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 📊 Preflight Summary: 9 episodes + 0 packs | Total: 9.26 GB
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 🎯 Downloads started immediately after each season analysis
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]    (No waiting for all analysis to complete - optimal efficiency!)
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDERR] [+0:03:45] 2025-09-18 09:13:00,120 - interactive_pipeline_01 - WARNING - Preflight skipped: missing Sonarr series id
[2025-09-18 09:13:00] [STDERR] [+0:03:45] 2025-09-18 09:13:00,123 - interactive_pipeline_01 - INFO - Stored enhanced TV metadata for: Futurama (1999)
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] ============================================================
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 📊 TV Download Summary (compact)
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] ============================================================
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] • Steven Universe (2013) S02 → Unknown
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] • Futurama (1999) S01 → Unknown
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] ============================================================
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 📊 Processing Complete!
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] ============================================================
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 🎬 Movies processed: 0
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 📺 TV shows processed: 2
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 📊 Total content processed: 2
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 🔍 Verifying 1 downloads actually started...
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:45]    This replaces guesswork with real verification!
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDERR] [+0:03:45] 2025-09-18 09:13:00,125 - interactive_pipeline_01 - INFO - 🔄 Starting real-time download monitoring (interval: 5s)
[2025-09-18 09:13:00] [STDERR] [+0:03:45] 2025-09-18 09:13:00,127 - interactive_pipeline_01 - INFO - 📦 SABnzbd match found: 'Steven Universe (2013)' -> 'steven.universe.s02e10.1080p.bluray.x264-taxes' (similarity: 1.00)
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] ✅ Download verified: "Steven Universe (2013)" is now downloading
[2025-09-18 09:13:00] [STDOUT] [+0:03:45] 
[2025-09-18 09:13:00] [STDERR] [+0:03:45] 2025-09-18 09:13:00,127 - interactive_pipeline_01 - INFO - {"timestamp": "2025-09-18T09:13:00.127838", "event": "download_verified", "job_id": "sonarr_369_series", "title": "Steven Universe (2013)", "source": "sonarr", "status": "downloading", "progress": 0.5508446830801467, "size_total": *********, "size_downloaded": *********, "speed_bps": 0.0, "eta": "0:00:05", "sonarr_id": 369, "sab_nzo_id": "SABnzbd_nzo_b383huqu", "quality": "Unknown"}
[2025-09-18 09:13:00] [STDERR] [+0:03:46] 2025-09-18 09:13:00,254 - interactive_pipeline_01 - INFO - 🔧 Intelligent Fallback System initialized
[2025-09-18 09:13:00] [STDERR] [+0:03:46] 2025-09-18 09:13:00,254 - interactive_pipeline_01 - INFO -    📁 Preflight decisions: workspace\preflight_decisions
[2025-09-18 09:13:00] [STDERR] [+0:03:46] 2025-09-18 09:13:00,254 - interactive_pipeline_01 - INFO -    🎬 Radarr URL: http://localhost:7878
[2025-09-18 09:13:00] [STDERR] [+0:03:46] 2025-09-18 09:13:00,254 - interactive_pipeline_01 - INFO -    🔑 API Key configured: ✅
[2025-09-18 09:13:00] [STDERR] [+0:03:46] 2025-09-18 09:13:00,254 - interactive_pipeline_01 - INFO -    📊 Telemetry integration: ✅
[2025-09-18 09:13:00] [STDERR] [+0:03:46] 2025-09-18 09:13:00,254 - interactive_pipeline_01 - INFO - 🛡️ Intelligent fallback system initialized with telemetry integration
[2025-09-18 09:13:00] [STDOUT] [+0:03:46] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:46] 📊 Real-time telemetry monitoring started
[2025-09-18 09:13:00] [STDOUT] [+0:03:46] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:46] 📄 Telemetry dashboard log: logs\telemetry_dashboard_2025-09-18_09-13-00-AM.txt
[2025-09-18 09:13:00] [STDOUT] [+0:03:46] 
[2025-09-18 09:13:00] [STDOUT] [+0:03:46]    (Dashboard output will be written to separate file to keep main log clean)
[2025-09-18 09:13:00] [STDOUT] [+0:03:46] 
[2025-09-18 09:13:10] [STDERR] [+0:03:56] 2025-09-18 09:13:10,347 - interactive_pipeline_01 - INFO - 🔄 Triggering post-processing for completed download: Steven Universe (2013)
[2025-09-18 09:13:10] [STDERR] [+0:03:56] 2025-09-18 09:13:10,350 - interactive_pipeline_01 - INFO - ✅ Post-processing triggered for: Steven Universe (2013) (PID: 15968)
